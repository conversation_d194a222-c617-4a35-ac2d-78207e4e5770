(()=>{var e={};e.id=13,e.ids=[13],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},18878:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>c}),s(3517),s(54302),s(12523);var r=s(23191),a=s(88716),l=s(37922),i=s.n(l),n=s(95231),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let c=["",{children:["freelancer",{children:["settings",{children:["personal-info",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,3517)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\settings\\personal-info\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\freelancer\\settings\\personal-info\\page.tsx"],m="/freelancer/settings/personal-info/page",u={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/freelancer/settings/personal-info/page",pathname:"/freelancer/settings/personal-info",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},95919:(e,t,s)=>{Promise.resolve().then(s.bind(s,91171))},6343:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},47546:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},31540:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},18019:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},48705:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},98091:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(80851).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},91171:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>q});var r=s(10326),a=s(25842),l=s(92166),i=s(17577),n=s(74064),o=s(74723),c=s(27256),d=s(83855),m=s(94019),u=s(37956),x=s(29752),p=s(82015),h=s(82287),f=s(36283),g=s(31540),j=s(98091);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let v=(0,s(80851).Z)("CloudUpload",[["path",{d:"M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242",key:"1pljnt"}],["path",{d:"M12 12v9",key:"192myk"}],["path",{d:"m16 16-4-4-4 4",key:"119tzi"}]]);var b=s(91664),N=s(56627),y=s(6260);let w=["application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],E=({onResumeUpdate:e,refreshTrigger:t,userId:s})=>{let[a,l]=(0,i.useState)(null),[n,o]=(0,i.useState)(null),[c,d]=(0,i.useState)(!1),[u,x]=(0,i.useState)(null),[p,h]=(0,i.useState)(null),[E,I]=(0,i.useState)(!1),C=(0,i.useRef)(null),P=e=>{if(!e)return"";let t=e.includes(".")?e.substring(e.lastIndexOf(".")):"";return e.length>20?`${e.substring(0,20-t.length)}...${t}`:e},k=e=>{try{let t=e.split("/");return t[t.length-1].split("?")[0]||"resume.pdf"}catch{return"resume.pdf"}},D=async()=>{try{I(!0),await y.b.put("/freelancer",{resume:null}),h(null),o(null),x(null),(0,N.Am)({title:"Success",description:"Resume removed successfully!"})}catch(e){(0,N.Am)({variant:"destructive",title:"Error",description:"Failed to remove resume. Please try again."})}finally{I(!1)}},A=async t=>{if(t.preventDefault(),!a){(0,N.Am)({variant:"destructive",title:"No Resume Selected",description:"Please select a resume before uploading."});return}let s=new FormData;s.append("resume",a);try{d(!0);let{Location:t}=(await y.b.post("/register/upload-image",s,{headers:{"Content-Type":"multipart/form-data"}})).data.data;if(!t)throw Error("Failed to upload the resume.");let r=await y.b.put("/freelancer",{resume:t});if(200===r.status)h(t),o(a.name),l(null),x(t),e&&e(),(0,N.Am)({title:"Success",description:"Resume uploaded successfully!"});else throw Error("Failed to update resume.")}catch(e){(0,N.Am)({variant:"destructive",title:"Error",description:"Something went wrong. Please try again."})}finally{d(!1)}};(0,i.useEffect)(()=>{(async()=>{try{let e=s?`/freelancer/${s}`:"/freelancer",t=await y.b.get(e),r=t.data?.data?.resume||t.data?.resume;r&&""!==r.trim()?(h(r),o(k(r)),x(r)):(h(null),o(null),x(null))}catch(e){console.error("Error fetching resume:",e)}})()},[s]);let S=e=>{e.stopPropagation(),l(null),x(null),p||o(null)};return r.jsx("div",{className:"upload-form max-w-md mx-auto rounded shadow-md p-4",children:(0,r.jsxs)("div",{className:"space-y-6 flex flex-col items-center",children:[p&&!a?(0,r.jsxs)("div",{className:"w-full border border-border rounded-lg p-4",children:[r.jsx("div",{className:"flex items-center justify-between mb-3",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[r.jsx(f.Z,{className:"text-green-600 dark:text-green-400 w-8 h-8"}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"font-medium text-foreground",children:"Resume Uploaded"}),r.jsx("p",{className:"text-sm text-muted-foreground",children:P(n||"resume.pdf")})]})]})}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)(b.z,{variant:"outline",size:"sm",onClick:()=>window.open(p,"_blank"),className:"flex items-center gap-2",children:[r.jsx(g.Z,{className:"w-4 h-4"}),"View"]}),(0,r.jsxs)(b.z,{variant:"destructive",size:"sm",onClick:D,disabled:E,className:"flex items-center gap-2",children:[r.jsx(j.Z,{className:"w-4 h-4"}),E?"Removing...":"Remove"]})]})]}):r.jsx("div",{className:"flex flex-col items-center justify-center border-dashed border-2 border-muted-foreground/25 rounded-lg p-6 w-full cursor-pointer hover:border-muted-foreground/50 transition-colors",onClick:()=>C.current?.click(),children:a?(0,r.jsxs)("div",{className:"w-full flex flex-col items-center gap-4 text-foreground text-center",children:[(0,r.jsxs)("div",{className:"flex flex-1 gap-6",children:[r.jsx("p",{className:"truncate",children:P(a.name)}),r.jsx("button",{className:"bg-red-600 text-white rounded-full p-1 hover:bg-red-700 transition-colors",onClick:e=>S(e),"aria-label":"Remove file",children:r.jsx(m.Z,{className:"w-4 h-4"})})]}),u?r.jsx("iframe",{src:u,title:"Resume Preview",className:"w-full h-40 border border-border rounded"}):(0,r.jsxs)("div",{className:"flex items-center space-x-2 p-2 bg-muted rounded",children:[r.jsx(f.Z,{className:"text-muted-foreground w-6 h-6"}),r.jsx("span",{className:"text-muted-foreground text-sm",children:P(a.name)})]})]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(v,{className:"text-muted-foreground w-12 h-12 mb-2"}),r.jsx("p",{className:"text-foreground text-center",children:p?"Select a new resume to replace the current one":"Drag and drop your resume here or click to upload"}),r.jsx("div",{className:"flex items-center mt-2",children:r.jsx("span",{className:"text-muted-foreground text-xs md:text-sm",children:"Supported formats: PDF, DOCX."})}),r.jsx("input",{type:"file",accept:w.join(","),onChange:e=>{let t=e.target.files?.[0];t&&(w.includes(t.type)?t.size<=5242880?(l(t),o(t.name),"application/pdf"===t.type?x(URL.createObjectURL(t)):x(null)):(0,N.Am)({variant:"destructive",title:"File too large",description:"Resume size should not exceed 5MB."}):(0,N.Am)({variant:"destructive",title:"Invalid file type",description:"Supported formats: PDF, DOCX."}))},className:"hidden",ref:C})]})}),a&&r.jsx(b.z,{onClick:A,className:"w-full",disabled:c,children:c?"Uploading...":"Upload Resume"}),n&&(0,r.jsxs)("p",{className:"text-center text-muted-foreground",children:["Uploaded:"," ",r.jsx("strong",{className:"text-foreground",children:P(n||"")})]})]})})};var I=s(18019),C=s(9969),P=s(82631);let k=({value:e="",onChange:t,error:s})=>{let[a,l]=(0,i.useState)(0);(0,i.useEffect)(()=>{l(e.trim().split(/\s+/).filter(e=>e.length>0).length)},[e]);let n=a>=500,o=(0,r.jsxs)("div",{className:"max-w-xs",children:[r.jsx("p",{className:"font-medium mb-2",children:"Cover Letter Tips:"}),(0,r.jsxs)("ul",{className:"text-xs space-y-1",children:[r.jsx("li",{children:"• Introduce yourself and your relevant experience"}),r.jsx("li",{children:"• Explain why you are interested in this type of work"}),r.jsx("li",{children:"• Highlight your key skills and achievements"}),r.jsx("li",{children:"• Mention specific technologies or tools you are proficient with"}),r.jsx("li",{children:"• Describe your work style and communication approach"}),r.jsx("li",{children:"• Write at least 500 words for a complete cover letter"})]})]});return(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(C.NI,{children:(0,r.jsxs)("div",{className:"relative",children:[r.jsx(p.g,{placeholder:"Write your cover letter here... (optional - minimum 500 words if provided)",value:e,onChange:e=>{let s=e.target.value;s.trim().split(/\s+/).filter(e=>e.length>0).length<=500&&t(s)},className:`min-h-[200px] resize-y pr-10 text-foreground placeholder:text-muted-foreground ${s?"border-red-500 focus:border-red-500":""}`,rows:10}),r.jsx(P.TooltipProvider,{children:(0,r.jsxs)(P.u,{children:[r.jsx(P.aJ,{asChild:!0,children:r.jsx("button",{type:"button",className:"absolute top-3 right-3 p-1 rounded-full hover:bg-muted transition-colors",children:r.jsx(I.Z,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"})})}),r.jsx(P._v,{side:"left",className:"max-w-xs",children:o})]})})]})}),e&&e.trim().length>0&&(0,r.jsxs)("div",{className:"flex justify-between items-center text-sm",children:[(0,r.jsxs)("span",{className:`${n?"text-green-600 dark:text-green-400":"text-orange-500 dark:text-orange-400"}`,children:["Words: ",a,"/500 ",n?"✓":""]}),!n&&a>0&&r.jsx("span",{className:"text-muted-foreground text-xs",children:"Cover letter should be at least 500 words"})]}),(!e||0===e.trim().length)&&r.jsx("div",{className:"text-sm text-muted-foreground",children:"Cover letter is optional. Leave empty to skip, or write at least 500 words for a complete cover letter."}),s&&r.jsx(C.zG,{children:s})]})};var D=s(41190),A=s(78062),S=s(38443),R=s(29280),L=s(58285),T=s(39958);let z=c.z.object({firstName:c.z.string().min(2,{message:"First Name must be at least 2 characters."}),lastName:c.z.string().min(2,{message:"Last Name must be at least 2 characters."}),username:c.z.string().min(2,{message:"Username must be at least 2 characters."}).max(30,{message:"Username must not be longer than 30 characters."}),email:c.z.string().email(),phone:c.z.string().min(10,{message:"Phone number must be at least 10 digits."}),role:c.z.string(),personalWebsite:c.z.string().url().optional(),coverLetter:c.z.string().optional().refine(e=>!e||""===e.trim()||e.trim().split(/\s+/).filter(e=>e.length>0).length>=500,{message:"Cover letter must contain at least 500 words when provided."}),description:c.z.string().max(500,{message:"Description cannot exceed 500 characters."})});function F({user_id:e}){let[t,s]=(0,i.useState)({}),[a,l]=(0,i.useState)([]),[c,f]=(0,i.useState)([]),[g,j]=(0,i.useState)(""),[v,w]=(0,i.useState)([]),[I,P]=(0,i.useState)([]),[F,V]=(0,i.useState)(""),[_,q]=(0,i.useState)([]),[Z,M]=(0,i.useState)([]),[O,B]=(0,i.useState)(""),[G,U]=(0,i.useState)(!1),[W,$]=(0,i.useState)(!1),[J,X]=(0,i.useState)(0),[,H]=(0,i.useState)({skills:[],projectsDomains:[],domains:[]}),[Q,K]=(0,i.useState)({label:"",description:""}),[Y,ee]=(0,i.useState)({label:"",description:""}),[et,es]=(0,i.useState)({label:"",description:""}),[er]=(0,i.useState)(null),ea=(0,o.cI)({resolver:(0,n.F)(z),defaultValues:{firstName:"",lastName:"",username:"",email:"",phone:"",role:"",personalWebsite:"",coverLetter:"",description:""},mode:"all"}),el=()=>{(function(e,t,s){let r=e.trim();if(t.some(e=>e.label===r))return console.warn(`${r} already exists in the dropdown.`);s([...t,{label:r}])})(g,a,l),g&&!c.some(e=>e.name===g)&&(f([...c,{name:g,level:"",experience:"",interviewStatus:T.sB.PENDING,interviewInfo:"",interviewerRating:0}]),H(e=>({...e,skills:[...e.skills,{name:g}]})),j(""))},ei=async()=>{if(!Q.label.trim()){console.warn("Field is required.");return}let t={label:Q.label,interviewInfo:Q.description,createdBy:L.Dy.FREELANCER,createdById:e,status:T.sB.ACTIVE};try{await y.b.post("/skills",t);let e=[...a,{label:Q.label}];w(e),f([...c,{name:Q.label,level:"",experience:"",interviewStatus:"PENDING",interviewInfo:Q.description,interviewerRating:0}]),K({label:"",description:""}),$(!1)}catch(e){console.error("Failed to add skill:",e.response?.data||e.message),(0,N.Am)({variant:"destructive",title:"Error",description:"Failed to add skill. Please try again."})}finally{U(!1)}},en=async()=>{if(!Y.label.trim()){console.warn("Field is required.");return}let t={label:Y.label,interviewInfo:Q.description,createdBy:L.Dy.FREELANCER,createdById:e,status:T.sB.ACTIVE};try{await y.b.post("/domain",t);let e=[...v,{label:Y.label}];w(e),P([...I,{name:Y.label,level:"",experience:"",interviewStatus:"PENDING",interviewInfo:Y.description,interviewerRating:0}]),ee({label:"",description:""}),$(!1)}catch(e){console.error("Failed to add domain:",e.response?.data||e.message),(0,N.Am)({variant:"destructive",title:"Error",description:"Failed to add domain. Please try again."})}finally{U(!1)}},eo=async()=>{if(!et.label.trim()){console.warn("Field is required.");return}let t={label:et.label,createdBy:L.Dy.FREELANCER,createdById:e,status:T.sB.ACTIVE};try{await y.b.post("/projectdomain",t);let e=[..._,{label:et.label}];q(e),M([...Z,{name:et.label,level:"",experience:"",interviewStatus:"PENDING",interviewInfo:et.description,interviewerRating:0}]),es({label:"",description:""}),$(!1)}catch(e){console.error("Failed to add project domain:",e.response?.data||e.message),(0,N.Am)({variant:"destructive",title:"Error",description:"Failed to add project domain. Please try again."})}finally{U(!1)}},ec=()=>{(function(e,t,s){let r=e.trim();if(t.some(e=>e.label===r))return console.warn(`${r} already exists in the dropdown.`);s([...t,{label:r}])})(F,v,w),F&&!I.some(e=>e.name===F)&&(P([...I,{name:F,level:"",experience:"",interviewStatus:T.sB.PENDING,interviewInfo:"",interviewerRating:0}]),H(e=>({...e,domains:[...e.domains,{name:F}]})),V(""))},ed=()=>{(function(e,t,s){let r=e.trim();if(t.some(e=>e.label===r))return console.warn(`${r} already exists in the dropdown.`);s([...t,{label:r}])})(O,_,q),O&&!Z.some(e=>e.name===e)&&(M([...Z,{name:O,level:"",experience:"",interviewStatus:T.sB.PENDING,interviewInfo:"",interviewerRating:0}]),H(e=>({...e,projectsDomains:[...e.projectsDomains,{name:O}]})),B(""))},em=e=>{f(c.filter(t=>t.name!==e))},eu=e=>{P(I.filter(t=>t.name!==e))},ex=e=>{M(Z.filter(t=>t.name!==e))},[ep,eh]=(0,i.useState)("");async function ef(e){U(!0);try{let{...r}=e,a=c.map(e=>({...e,interviewInfo:e.interviewInfo||"",interviewerRating:e.interviewerRating||0,interviewStatus:e.interviewStatus||"PENDING"}));await y.b.put("/freelancer",{...r,coverLetter:e.coverLetter,skills:a,domain:I,projectDomain:Z,description:e.description}),s({...t,firstName:e.firstName,lastName:e.lastName,userName:e.username,email:e.email,phone:e.phone,role:e.role,personalWebsite:e.personalWebsite,coverLetter:e.coverLetter,skills:a,domain:I,projectDomains:Z}),(0,N.Am)({title:"Profile Updated",description:"Your profile has been successfully updated."}),setTimeout(()=>{X(e=>e+1)},500)}catch(e){console.error("API Error:",e),(0,N.Am)({variant:"destructive",title:"Error",description:"Failed to update profile. Please try again later."})}finally{U(!1)}}return r.jsx(x.Zb,{className:"p-10",children:(0,r.jsxs)(C.l0,{...ea,children:[r.jsx(h.Z,{profile:t.profilePic,entityType:L.Dy.FREELANCER}),(0,r.jsxs)("form",{onSubmit:ea.handleSubmit(ef),className:"grid gap-10 grid-cols-1 sm:grid-cols-2 mt-4",children:[r.jsx(C.Wi,{control:ea.control,name:"firstName",render:({field:e})=>(0,r.jsxs)(C.xJ,{children:[r.jsx(C.lX,{children:"First Name"}),r.jsx(C.NI,{children:r.jsx(D.I,{placeholder:"Enter your first name",...e})}),r.jsx(C.zG,{})]})}),r.jsx(C.Wi,{control:ea.control,name:"lastName",render:({field:e})=>(0,r.jsxs)(C.xJ,{children:[r.jsx(C.lX,{children:"Last Name"}),r.jsx(C.NI,{children:r.jsx(D.I,{placeholder:"Enter your last name",...e})}),r.jsx(C.zG,{})]})}),r.jsx(C.Wi,{control:ea.control,name:"username",render:({field:e})=>(0,r.jsxs)(C.xJ,{children:[r.jsx(C.lX,{children:"Username"}),r.jsx(C.NI,{children:r.jsx(D.I,{placeholder:"Enter your username",...e,readOnly:!0})}),r.jsx(C.zG,{}),r.jsx(C.pf,{children:"Non editable field"})]})}),r.jsx(C.Wi,{control:ea.control,name:"email",render:({field:e})=>(0,r.jsxs)(C.xJ,{children:[r.jsx(C.lX,{children:"Email"}),r.jsx(C.NI,{children:r.jsx(D.I,{placeholder:"Enter your email",...e,readOnly:!0})}),r.jsx(C.pf,{children:"Non editable field"}),r.jsx(C.zG,{})]})}),r.jsx(C.Wi,{control:ea.control,name:"description",render:({field:e})=>(0,r.jsxs)(C.xJ,{className:"sm:col-span-2",children:[r.jsx(C.lX,{children:"Description"}),r.jsx(C.NI,{children:r.jsx(p.g,{placeholder:"Enter description",...e})}),r.jsx(C.zG,{})]})}),r.jsx(C.Wi,{control:ea.control,name:"phone",render:({field:e})=>(0,r.jsxs)(C.xJ,{children:[r.jsx(C.lX,{children:"Phone Number"}),r.jsx(C.NI,{children:r.jsx(D.I,{placeholder:"+91",...e,readOnly:!0})}),r.jsx(C.zG,{}),r.jsx(C.pf,{children:"Non editable field"})]})}),r.jsx(C.Wi,{control:ea.control,name:"personalWebsite",render:({field:e})=>(0,r.jsxs)(C.xJ,{children:[r.jsx(C.lX,{children:"Personal Website URL"}),r.jsx(C.NI,{children:r.jsx(D.I,{placeholder:"Enter your LinkedIn URL",type:"url",...e})}),r.jsx(C.pf,{children:"Enter your Personal Website URL"}),r.jsx(C.zG,{})]})}),r.jsx(A.Separator,{className:"col-span-2"}),r.jsx("div",{className:"sm:col-span-2",children:(0,r.jsxs)("div",{className:"grid gap-10 grid-cols-1 sm:grid-cols-6",children:[r.jsx("div",{className:"sm:col-span-2",children:(0,r.jsxs)("div",{className:"flex-1 min-w-[350px] max-w-[500px] mt-5",children:[r.jsx(C.lX,{children:"Skills"}),(0,r.jsxs)("div",{className:"flex items-center mt-2",children:[(0,r.jsxs)(R.Ph,{onValueChange:e=>{j(e),eh("")},value:g||"",onOpenChange:e=>{e||eh("")},children:[r.jsx(R.i4,{children:r.jsx(R.ki,{placeholder:g||"Select skill"})}),(0,r.jsxs)(R.Bw,{children:[(0,r.jsxs)("div",{className:"p-2 relative",children:[r.jsx("input",{type:"text",value:ep,onChange:e=>eh(e.target.value),className:"w-full p-2 border border-gray-300 rounded-lg text-sm",placeholder:"Search skills"}),ep&&r.jsx("button",{onClick:()=>eh(""),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-white text-xl transition-colors mr-2",children:"\xd7"})]}),a.filter(e=>e.label.toLowerCase().includes(ep.toLowerCase())&&!c.some(t=>t.name===e.label)).map((e,t)=>r.jsx(R.Ql,{value:e.label,children:e.label},t)),0===a.filter(e=>e.label.toLowerCase().includes(ep.toLowerCase())&&!c.some(t=>t.name===e.label)).length&&r.jsx("div",{className:"p-2 text-gray-500 italic text-center",children:"No matching skills"})]})]}),r.jsx(b.z,{variant:"outline",type:"button",size:"icon",className:"ml-2",disabled:!g,onClick:()=>{el(),j(""),eh("")},children:r.jsx(d.Z,{className:"h-4 w-4"})})]}),r.jsx("div",{className:"flex flex-wrap gap-2 mt-5",children:c.map((e,t)=>(0,r.jsxs)(S.C,{className:"uppercase text-xs font-normal bg-gray-300 flex items-center px-2 py-1",children:[e.name,r.jsx("button",{type:"button",onClick:()=>em(e.name),className:"ml-2 text-red-500 hover:text-red-700",children:r.jsx(m.Z,{className:"h-4 w-4"})})]},t))})]})}),r.jsx("div",{className:"sm:col-span-2",children:(0,r.jsxs)("div",{className:"flex-1 min-w-[350px] max-w-[500px] mt-5",children:[r.jsx(C.lX,{children:"Domains"}),(0,r.jsxs)("div",{className:"flex items-center mt-2",children:[(0,r.jsxs)(R.Ph,{onValueChange:e=>{V(e),eh("")},value:F||"",onOpenChange:e=>{e||eh("")},children:[r.jsx(R.i4,{children:r.jsx(R.ki,{placeholder:F||"Select domain"})}),(0,r.jsxs)(R.Bw,{children:[(0,r.jsxs)("div",{className:"p-2 relative",children:[r.jsx("input",{type:"text",value:ep,onChange:e=>eh(e.target.value),className:"w-full p-2 border border-gray-300 rounded-lg text-sm",placeholder:"Search domains"}),ep&&r.jsx("button",{onClick:()=>eh(""),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-white text-xl transition-colors mr-2",children:"\xd7"})]}),v.filter(e=>e.label.toLowerCase().includes(ep.toLowerCase())&&!I.some(t=>t.name===e.label)).map((e,t)=>r.jsx(R.Ql,{value:e.label,children:e.label},t)),0===v.filter(e=>e.label.toLowerCase().includes(ep.toLowerCase())&&!I.some(e=>e.name===v.name)).length&&r.jsx("div",{className:"p-2 text-gray-500 italic text-center",children:"No matching domains"})]})]}),r.jsx(b.z,{variant:"outline",type:"button",size:"icon",className:"ml-2",disabled:!F,onClick:()=>{ec(),V(""),eh("")},children:r.jsx(d.Z,{className:"h-4 w-4"})})]}),r.jsx("div",{className:"flex flex-wrap gap-2 mt-5",children:I.map((e,t)=>(0,r.jsxs)(S.C,{className:"uppercase text-xs font-normal bg-gray-300 flex items-center px-2 py-1",children:[e.name,r.jsx("button",{type:"button",onClick:()=>eu(e.name),className:"ml-2 text-red-500 hover:text-red-700",children:r.jsx(m.Z,{className:"h-4 w-4"})})]},t))})]})}),r.jsx("div",{className:"sm:col-span-2",children:(0,r.jsxs)("div",{className:"flex-1 min-w-[350px] max-w-[500px] mt-5",children:[r.jsx(C.lX,{children:"Project Domains"}),(0,r.jsxs)("div",{className:"flex items-center mt-2",children:[(0,r.jsxs)(R.Ph,{onValueChange:e=>{B(e),eh("")},value:O||"",onOpenChange:e=>{e||eh("")},children:[r.jsx(R.i4,{children:r.jsx(R.ki,{placeholder:O||"Select project domain"})}),(0,r.jsxs)(R.Bw,{children:[(0,r.jsxs)("div",{className:"p-2 relative",children:[r.jsx("input",{type:"text",value:ep,onChange:e=>eh(e.target.value),className:"w-full p-2 border border-gray-300 rounded-lg text-sm",placeholder:"Search project domains"}),ep&&r.jsx("button",{onClick:()=>eh(""),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-white text-xl transition-colors mr-2",children:"\xd7"})]}),_.filter(e=>e.label.toLowerCase().includes(ep.toLowerCase())&&!Z.some(t=>t.name===e.label)).map((e,t)=>r.jsx(R.Ql,{value:e.label,children:e.label},t)),0===_.filter(e=>e.label.toLowerCase().includes(ep.toLowerCase())&&!Z.some(e=>e.name===_.name)).length&&r.jsx("div",{className:"p-2 text-gray-500 italic text-center",children:"No matching domains"})]})]}),r.jsx(b.z,{variant:"outline",type:"button",size:"icon",className:"ml-2",disabled:!O,onClick:()=>{ed(),B(""),eh("")},children:r.jsx(d.Z,{className:"h-4 w-4"})})]}),r.jsx("div",{className:"flex flex-wrap gap-2 mt-5",children:Z.map((e,t)=>(0,r.jsxs)(S.C,{className:"uppercase text-xs font-normal bg-gray-300 flex items-center px-2 py-1",children:[e.name,r.jsx("button",{type:"button",onClick:()=>ex(e.name),className:"ml-2 text-red-500 hover:text-red-700",children:r.jsx(m.Z,{className:"h-4 w-4"})})]},t))})]})})]})}),r.jsx(A.Separator,{className:"col-span-2 mt-0"}),r.jsx("div",{className:"col-span-2",children:(0,r.jsxs)("div",{className:"grid gap-10 grid-cols-1 sm:grid-cols-2",children:[(0,r.jsxs)("div",{className:"flex flex-col items-start",children:[r.jsx(C.lX,{className:"ml-2",children:"Upload Resume"}),r.jsx("div",{className:"w-full",children:r.jsx(E,{refreshTrigger:J,userId:e})})]}),r.jsx(C.Wi,{control:ea.control,name:"coverLetter",render:({field:e,fieldState:t})=>(0,r.jsxs)(C.xJ,{className:"flex flex-col items-start",children:[r.jsx(C.lX,{className:"ml-2",children:"Cover Letter (Optional)"}),r.jsx("div",{className:"w-full",children:r.jsx(k,{value:e.value||"",onChange:e.onChange,error:t.error?.message})})]})})]})}),r.jsx("div",{className:"col-span-2",children:r.jsx(b.z,{type:"submit",className:"sm:col-span-2 w-full",disabled:G,children:G?"Loading...":"Update Profile"})}),W&&(0,r.jsxs)(u.Vq,{open:W,onOpenChange:e=>$(e),children:[r.jsx(u.t9,{className:"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-40"}),r.jsx(u.cZ,{className:"fixed inset-0 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-black rounded-md shadow-xl p-6 w-[90%] max-w-md",children:["skill"===er&&(0,r.jsxs)(r.Fragment,{children:[r.jsx("h2",{className:"text-lg font-semibold text-white mb-4",children:"Add New Skill"}),(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),ei()},children:[(0,r.jsxs)("div",{className:"mb-4",children:[r.jsx("label",{htmlFor:"skillLabel",className:"block text-sm font-medium text-white mb-1",children:"Skill Label"}),r.jsx("input",{type:"text",value:Q.label,onChange:e=>K({...Q,label:e.target.value}),placeholder:"Enter skill label",className:"w-full px-3 py-2 rounded-md text-white bg-black placeholder-gray-400 border border-white",required:!0})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3",children:[r.jsx(b.z,{type:"button",variant:"ghost",onClick:()=>$(!1),className:"mt-3",children:"Cancel"}),r.jsx(b.z,{type:"button",className:"mt-3",onClick:()=>{ei(),K({label:"",description:""})},children:"Add Skill"})]})]})]}),"domain"===er&&(0,r.jsxs)(r.Fragment,{children:[r.jsx("h2",{className:"text-lg font-semibold text-white mb-4",children:"Add New Domain"}),(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),en()},children:[(0,r.jsxs)("div",{className:"mb-4",children:[r.jsx("label",{htmlFor:"domainLabel",className:"block text-sm font-medium text-white mb-1",children:"Domain Label"}),r.jsx("input",{type:"text",value:Y.label,onChange:e=>ee({...Y,label:e.target.value}),placeholder:"Enter Domain label",className:"w-full px-3 py-2 rounded-md text-white bg-black placeholder-gray-400 border border-white",required:!0})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3",children:[r.jsx(b.z,{type:"button",variant:"ghost",onClick:()=>$(!1),className:"mt-3",children:"Cancel"}),r.jsx(b.z,{type:"button",className:"mt-3",onClick:()=>{en(),ee({label:"",description:""})},children:"Add Domain"})]})]})]}),"projectDomain"===er&&(0,r.jsxs)(r.Fragment,{children:[r.jsx("h2",{className:"text-lg font-semibold text-white mb-4",children:"Add New Project Domain"}),(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),eo()},children:[(0,r.jsxs)("div",{className:"mb-4",children:[r.jsx("label",{htmlFor:"projectDomainLabel",className:"block text-sm font-medium text-white mb-1",children:"Project Domain Label"}),r.jsx("input",{type:"text",value:et.label,onChange:e=>es({...et,label:e.target.value}),placeholder:"Enter Project Domain label",className:"w-full px-3 py-2 rounded-md text-white bg-black placeholder-gray-400 border border-white",required:!0})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3",children:[r.jsx(b.z,{type:"button",variant:"ghost",onClick:()=>$(!1),className:"mt-3",children:"Cancel"}),r.jsx(b.z,{type:"button",className:"mt-3",onClick:()=>{eo(),es({label:"",description:""})},children:"Add Project Domain"})]})]})]})]})})]})]})]})})}var V=s(45175),_=s(40588);function q(){let e=(0,a.v9)(e=>e.user);return(0,r.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[r.jsx(l.Z,{menuItemsTop:V.y,menuItemsBottom:V.$,active:"Personal Info",isKycCheck:!0}),(0,r.jsxs)("div",{className:"flex flex-col sm:gap-8 sm:py-0 sm:pl-14 mb-8",children:[r.jsx(_.Z,{menuItemsTop:V.y,menuItemsBottom:V.$,activeMenu:"Personal Info",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Settings",link:"#"},{label:"Personal Info",link:"#"}]}),r.jsx("main",{className:"grid flex-1 items-start  sm:px-6 sm:py-0 md:gap-8",children:r.jsx(F,{user_id:e.uid})})]})]})}},82287:(e,t,s)=>{"use strict";s.d(t,{Z:()=>f});var r=s(10326),a=s(17577);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,s(80851).Z)("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);var i=s(83855),n=s(77506),o=s(46226),c=s(25842),d=s(56627),m=s(91664),u=s(6260),x=s(4594),p=s(58285);let h=["image/png","image/jpeg","image/jpg","image/gif","image/svg+xml"],f=({profile:e,entityType:t})=>{let s=(0,c.v9)(e=>e.user),f=(0,c.I0)(),[g,j]=(0,a.useState)(null),[v,b]=(0,a.useState)(e),[N,y]=(0,a.useState)(!1),w=(0,a.useRef)(null),E=async e=>{if(e.preventDefault(),!g){(0,d.Am)({variant:"destructive",title:"No Image Selected",description:"Please select an image before submitting."});return}y(!0);let r=new FormData;r.append("profilePicture",g);try{let{Location:e}=(await u.b.post("/register/upload-image",r,{headers:{"Content-Type":"multipart/form-data"}})).data.data;f((0,x.av)({...s,photoURL:e}));let a=t===p.Dy.FREELANCER?"/freelancer":"/business",l=await u.b.put(a,{profilePic:e});if(200===l.status)(0,d.Am)({title:"Success",description:"Profile picture uploaded successfully!"});else throw Error("Failed to update profile picture")}catch(e){console.error("Error during upload:",e),(0,d.Am)({variant:"destructive",title:"Upload failed",description:"Image upload failed. Please try again."})}finally{y(!1)}};return r.jsx("div",{className:"upload-form max-w-md mx-auto rounded shadow-md",children:(0,r.jsxs)("form",{onSubmit:E,className:"space-y-6",children:[r.jsx("input",{type:"file",accept:h.join(","),onChange:e=>{let t=e.target.files?.[0];t&&h.includes(t.type)?t.size<=1048576?(j(t),b(URL.createObjectURL(t))):(0,d.Am)({variant:"destructive",title:"File too large",description:"Image size should not exceed 1MB."}):(0,d.Am)({variant:"destructive",title:"Invalid file type",description:`Please upload a valid image file. Allowed formats: ${h.join(", ")}`})},className:"hidden",ref:w}),r.jsx("div",{className:"relative flex flex-col items-center",children:(0,r.jsxs)("label",{htmlFor:"file-input",className:"cursor-pointer relative",children:[v?r.jsx(o.default,{width:28,height:28,src:v,alt:"Avatar Preview",className:"w-28 h-28 rounded-full object-cover border-2 border-black-300"}):r.jsx("div",{className:"w-28 h-28 rounded-full bg-gray-700 flex items-center justify-center",children:r.jsx(o.default,{width:112,height:112,src:e,alt:"Avatar Preview",className:"w-28 h-28 rounded-full object-cover border-2 border-black-300"})}),r.jsx(m.z,{variant:"outline",type:"button",size:"icon",className:"absolute bottom-0 right-0 w-10 h-10 rounded-full bg-black border border-gray-300 flex items-center justify-center shadow-md",onClick:()=>{v?b(null):w.current?.click()},children:v?r.jsx(l,{className:"h-4 w-4 text-gray-400"}):r.jsx(i.Z,{className:"h-4 w-4 text-gray-400"})})]})}),v&&r.jsx(m.z,{type:"submit",className:"w-full",disabled:!g||N,children:N?(0,r.jsxs)(r.Fragment,{children:[r.jsx(n.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Please wait"]}):"Upload Profile Picture"})]})})}},9969:(e,t,s)=>{"use strict";s.d(t,{NI:()=>f,Wi:()=>m,l0:()=>c,lX:()=>h,pf:()=>g,xJ:()=>p,zG:()=>j});var r=s(10326),a=s(17577),l=s(99469),i=s(74723),n=s(51223),o=s(44794);let c=i.RV,d=a.createContext({}),m=({...e})=>r.jsx(d.Provider,{value:{name:e.name},children:r.jsx(i.Qr,{...e})}),u=()=>{let e=a.useContext(d),t=a.useContext(x),{getFieldState:s,formState:r}=(0,i.Gc)(),l=s(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=t;return{id:n,name:e.name,formItemId:`${n}-form-item`,formDescriptionId:`${n}-form-item-description`,formMessageId:`${n}-form-item-message`,...l}},x=a.createContext({}),p=a.forwardRef(({className:e,...t},s)=>{let l=a.useId();return r.jsx(x.Provider,{value:{id:l},children:r.jsx("div",{ref:s,className:(0,n.cn)("space-y-2",e),...t})})});p.displayName="FormItem";let h=a.forwardRef(({className:e,...t},s)=>{let{error:a,formItemId:l}=u();return r.jsx(o.Label,{ref:s,className:(0,n.cn)(a&&"text-destructive",e),htmlFor:l,...t})});h.displayName="FormLabel";let f=a.forwardRef(({...e},t)=>{let{error:s,formItemId:a,formDescriptionId:i,formMessageId:n}=u();return r.jsx(l.g7,{ref:t,id:a,"aria-describedby":s?`${i} ${n}`:`${i}`,"aria-invalid":!!s,...e})});f.displayName="FormControl";let g=a.forwardRef(({className:e,...t},s)=>{let{formDescriptionId:a}=u();return r.jsx("p",{ref:s,id:a,className:(0,n.cn)("text-sm text-muted-foreground",e),...t})});g.displayName="FormDescription";let j=a.forwardRef(({className:e,children:t,...s},a)=>{let{error:l,formMessageId:i}=u(),o=l?String(l?.message):t;return o?r.jsx("p",{ref:a,id:i,className:(0,n.cn)("text-sm font-medium text-destructive",e),...s,children:o}):null});j.displayName="FormMessage"},44794:(e,t,s)=>{"use strict";s.r(t),s.d(t,{Label:()=>c});var r=s(10326),a=s(17577),l=s(34478),i=s(28671),n=s(51223);let o=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef(({className:e,...t},s)=>r.jsx(l.f,{ref:s,className:(0,n.cn)(o(),e),...t}));c.displayName=l.f.displayName},78062:(e,t,s)=>{"use strict";s.r(t),s.d(t,{Separator:()=>n});var r=s(10326),a=s(17577),l=s(90220),i=s(51223);let n=a.forwardRef(({className:e,orientation:t="horizontal",decorative:s=!0,...a},n)=>r.jsx(l.f,{ref:n,decorative:s,orientation:t,className:(0,i.cn)("shrink-0 bg-border","horizontal"===t?"h-[1px] w-full":"h-full w-[1px]",e),...a}));n.displayName=l.f.displayName},45175:(e,t,s)=>{"use strict";s.d(t,{$:()=>u,y:()=>m});var r=s(10326),a=s(95920),l=s(79635),i=s(47546),n=s(48705),o=s(6343);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let c=(0,s(80851).Z)("ImagePlus",[["path",{d:"M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7",key:"31hg93"}],["line",{x1:"16",x2:"22",y1:"5",y2:"5",key:"ez7e4s"}],["line",{x1:"19",x2:"19",y1:"2",y2:"8",key:"1gkr8c"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);var d=s(46226);let m=[{href:"#",icon:r.jsx(d.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:r.jsx(a.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/freelancer/settings/personal-info",icon:r.jsx(l.Z,{className:"h-5 w-5"}),label:"Personal Info"},{href:"/freelancer/settings/professional-info",icon:r.jsx(i.Z,{className:"h-5 w-5"}),label:"Professional Info"},{href:"/freelancer/settings/projects",icon:r.jsx(n.Z,{className:"h-5 w-5"}),label:"Projects"},{href:"/freelancer/settings/education-info",icon:r.jsx(o.Z,{className:"h-5 w-5"}),label:"Education"},{href:"/freelancer/settings/resume",icon:r.jsx(c,{className:"h-5 w-5"}),label:"Portfolio"}],u=[]},58285:(e,t,s)=>{"use strict";var r,a,l,i,n,o,c,d,m,u,x;s.d(t,{Dy:()=>u,Dz:()=>p});let p={BATCH:3};(function(e){e.PROJECT_HIRING="PROJECT_HIRING",e.SKILL_INTERVIEW="SKILL_INTERVIEW",e.DOMAIN_INTERVIEW="DOMAIN_INTERVIEW",e.TALENT_INTERVIEW="TALENT_INTERVIEW"})(r||(r={})),function(e){e.ADDED="Added",e.APPROVED="Approved",e.CLOSED="Closed",e.COMPLETED="Completed"}(a||(a={})),function(e){e.ACTIVE="Active",e.IN_ACTIVE="Inactive",e.NOT_VERIFIED="Not Verified"}(l||(l={})),function(e){e.BUSINESS="Business",e.FREELANCER="Freelancer",e.BOTH="Both"}(i||(i={})),function(e){e.ACTIVE="Active",e.IN_ACTIVE="Inactive"}(n||(n={})),function(e){e.APPLIED="APPLIED",e.NOT_APPLIED="NOT_APPLIED",e.APPROVED="APPROVED",e.FAILED="FAILED",e.STOPPED="STOPPED",e.REAPPLIED="REAPPLIED"}(o||(o={})),function(e){e.PENDING="Pending",e.ACCEPTED="Accepted",e.REJECTED="Rejected",e.PANEL="Panel",e.INTERVIEW="Interview"}(c||(c={})),function(e){e.ACTIVE="ACTIVE",e.INACTIVE="INACTIVE",e.ARCHIVED="ARCHIVED"}(d||(d={})),function(e){e.ACTIVE="Active",e.PENDING="Pending",e.INACTIVE="Inactive",e.CLOSED="Closed"}(m||(m={})),function(e){e.FREELANCER="FREELANCER",e.ADMIN="ADMIN",e.BUSINESS="BUSINESS"}(u||(u={})),function(e){e.CREATED="Created",e.CLOSED="Closed",e.ACTIVE="Active"}(x||(x={}))},39958:(e,t,s)=>{"use strict";var r,a,l;s.d(t,{cd:()=>r,d8:()=>i,kJ:()=>a,sB:()=>l}),function(e){e.Mastery="Mastery",e.Proficient="Proficient",e.Beginner="Beginner"}(r||(r={})),function(e){e.ACTIVE="Active",e.PENDING="Pending",e.REJECTED="Rejected",e.COMPLETED="Completed"}(a||(a={})),function(e){e.ACTIVE="ACTIVE",e.PENDING="PENDING",e.REJECTED="REJECTED",e.COMPLETED="COMPLETED"}(l||(l={}));let i={APPLIED:"bg-blue-500 text-white hover:text-black",PENDING:"bg-green-500 text-white hover:text-black",VERIFIED:"bg-yellow-500 text-black hover:text-black",REUPLOAD:"bg-red-500 text-white hover:text-black",STOPPED:"bg-red-500 text-white hover:text-black"}},3517:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>l,default:()=>n});var r=s(68570);let a=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\settings\personal-info\page.tsx`),{__esModule:l,$$typeof:i}=a;a.default;let n=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\freelancer\settings\personal-info\page.tsx#default`)},34478:(e,t,s)=>{"use strict";s.d(t,{f:()=>n});var r=s(17577),a=s(77335),l=s(10326),i=r.forwardRef((e,t)=>(0,l.jsx)(a.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var n=i},90220:(e,t,s)=>{"use strict";s.d(t,{f:()=>c});var r=s(17577),a=s(77335),l=s(10326),i="horizontal",n=["horizontal","vertical"],o=r.forwardRef((e,t)=>{let{decorative:s,orientation:r=i,...o}=e,c=n.includes(r)?r:i;return(0,l.jsx)(a.WV.div,{"data-orientation":c,...s?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...o,ref:t})});o.displayName="Separator";var c=o}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,6686,4736,6499,8066,588],()=>s(18878));module.exports=r})();