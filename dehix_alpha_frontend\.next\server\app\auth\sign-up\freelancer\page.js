(()=>{var e={};e.id=1238,e.ids=[1238],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},45184:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c}),r(82487),r(54302),r(12523);var s=r(23191),a=r(88716),n=r(37922),i=r.n(n),o=r(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c=["",{children:["auth",{children:["sign-up",{children:["freelancer",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,82487)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\auth\\sign-up\\freelancer\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\auth\\sign-up\\freelancer\\page.tsx"],u="/auth/sign-up/freelancer/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/auth/sign-up/freelancer/page",pathname:"/auth/sign-up/freelancer",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},24069:(e,t,r)=>{Promise.resolve().then(r.bind(r,36866))},37358:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(80851).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},11890:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(80851).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},36866:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>J});var s=r(10326),a=r(90434),n=r(40603),i=r(17577),o=r.n(i),l=r(35047),c=r(27256),d=r(10321),u=r(74723),m=r(74064),p=r(79635),h=r(47546),x=r(58038),f=r(66307),g=r(12714),j=r(91216),b=r(77506),w=r(24230),v=r(86333),y=r(45691),N=r(89124),C=r(57776),P=r(51223),k=r(26408),_=r(91664),z=r(6260),D=r(56627),A=r(44794),Z=r(9969),F=r(41190),R=r(27918),S=r(37358),q=r(98181);let E=({selectedMonth:e,onSelect:t})=>s.jsx("div",{className:"grid grid-cols-3 gap-4 mb-4",children:Array.from({length:12},(e,t)=>(0,q.WU)(new Date(2e3,t,1),"MMMM")).map((r,a)=>s.jsx("button",{onClick:()=>t(a),className:`p-2 text-sm rounded-lg hover:bg-blue-600 transition ${e===a?"bg-blue-500 text-white":"text-white"}`,children:r},r))});var M=r(29280);let O=({selectedYear:e,onSelect:t})=>{let r=new Date().getFullYear()-16,a=Array.from({length:r-1970+1},(e,t)=>r-t).reverse();return(0,s.jsxs)(M.Ph,{onValueChange:e=>t(Number(e)),children:[s.jsx(M.i4,{className:"w-full",children:s.jsx(M.ki,{placeholder:e})}),s.jsx(M.Bw,{children:a.map(e=>s.jsx(M.Ql,{value:String(e),children:e},e))})]})},V=({onConfirm:e})=>s.jsx("button",{onClick:e,className:"mt-4 w-full bg-blue-600 text-white p-2 rounded-lg",children:"Confirm"});var I=r(33194),Y=r(51027),T=r(24118);let W=({field:e})=>{let t=new Date,r=new Date(t.setFullYear(t.getFullYear()-16)),a=e.value?new Date(e.value):void 0,[n,o]=(0,i.useState)(!1),[l,c]=(0,i.useState)(a?.getMonth()??r.getMonth()),[d,u]=(0,i.useState)(a?.getFullYear()??r.getFullYear()),[m,p]=(0,i.useState)(!1);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(Y.J2,{open:m,onOpenChange:p,children:[s.jsx(Y.xo,{asChild:!0,children:s.jsx(Z.NI,{children:(0,s.jsxs)(_.z,{type:"button",variant:"outline",className:`w-full justify-start text-left font-normal ${a?"":"text-muted-foreground"}`,onClick:()=>o(!0),children:[a?(0,q.WU)(a,"PPP"):"Pick a date",s.jsx(S.Z,{className:"ml-auto h-4 w-4 opacity-50"})]})})}),s.jsx(Y.yk,{className:"w-auto p-0",children:s.jsx(I.f,{mode:"single",selected:a,onSelect:t=>{e.onChange(t),p(!1)},fromYear:1970,toDate:r,defaultMonth:new Date(d,l)})})]}),s.jsx(T.Vq,{open:n,onOpenChange:o,children:(0,s.jsxs)(T.cZ,{className:"max-w-sm rounded-lg bg-[#111] mx-1 shadow-xl p-6",children:[s.jsx("h2",{className:"text-lg text-white mb-4",children:"Select Month & Year"}),s.jsx(E,{selectedMonth:l,onSelect:c}),s.jsx(O,{selectedYear:d,onSelect:u}),s.jsx(V,{onConfirm:()=>{o(!1),p(!0)}})]})})]})};function U({open:e,setOpen:t,setIsChecked:r}){return s.jsx(T.Vq,{open:e,onOpenChange:t,children:(0,s.jsxs)(T.cZ,{className:"max-w-5xl sm:mx-4 max-h-screen overflow-y-auto rounded-2xl p-6 shadow-lg",children:[s.jsx(T.fK,{children:s.jsx(T.$N,{className:"text-2xl font-bold  mb-4 text-center",children:"Terms & Conditions for Freelancer"})}),(0,s.jsxs)("div",{className:"space-y-6 text-sm  leading-relaxed px-2 sm:px-4",children:[(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"font-semibold text-base mb-1",children:"1. Registration and Account Management"}),s.jsx("p",{children:"By registering as a freelancer or client on the platform, users agree to abide by these terms and conditions. Users must provide accurate, complete, and up-to-date information during the registration process. Failure to do so may result in account suspension or termination. The platform reserves the right to approve, reject, or terminate user accounts at its sole discretion."})]}),(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"font-semibold text-base mb-1",children:"2. KYC Verification"}),s.jsx("p",{children:"All users are required to undergo KYC (Know Your Customer) verification to validate their profiles. Providing false or misleading information or documents during the KYC process may result in immediate account termination."})]}),(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"font-semibold text-base mb-1",children:"3. Platform Responsibilities"}),s.jsx("p",{children:"The platform acts as an intermediary connecting freelancers with potential clients. While the platform facilitates this connection, it does not guarantee job opportunities, successful contracts, or project outcomes. The platform is not liable for disputes, incomplete projects, or any issues arising from freelancer-client engagements."})]}),(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"font-semibold text-base mb-1",children:"4. User Conduct"}),s.jsx("p",{children:"Users are strictly prohibited from posting fraudulent job offers or misleading information. Spam, offensive language, and abusive behavior are not tolerated in any communications conducted through the platform. Violation of this rule may result in account suspension or termination."})]}),(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"font-semibold text-base mb-1",children:"5. Data Sharing and Privacy"}),s.jsx("p",{children:"By registering on the platform, users consent to sharing their contact details and profile information with clients seeking talent. The platform ensures that user data is shared only with verified clients for legitimate hiring purposes."})]}),(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"font-semibold text-base mb-1",children:"6. Termination of Accounts"}),s.jsx("p",{children:"Accounts may be terminated if users violate platform rules, provide false information, or fail to comply with KYC verification. The platform reserves the right to investigate and take appropriate action if fraudulent or suspicious activities are detected."})]}),s.jsx("div",{children:s.jsx("p",{children:"By using this platform, all users acknowledge and agree to these terms and conditions. The platform reserves the right to modify these terms at any time with prior notice to users."})})]}),s.jsx("div",{className:"mt-8 flex justify-end",children:s.jsx(_.z,{onClick:()=>{r(!0),t(!1)},className:"px-6 py-2 text-sm font-medium rounded-md bg-primary  hover:bg-primary/90 transition",children:"I Accept"})})]})})}let $=({currentStep:e=0})=>{let t=[{id:0,title:"Personal Info",icon:p.Z},{id:1,title:"Professional Info",icon:h.Z},{id:2,title:"Verification",icon:x.Z}];return(0,s.jsxs)("div",{className:"w-full max-w-5xl mx-auto py-4 sm:py-6 mb-10 sm:mb-8",children:[(0,s.jsxs)("div",{className:"text-center space-y-2 sm:space-y-4",children:[(0,s.jsxs)("h1",{className:"text-3xl font-bold",children:["Create Your Freelancer ",s.jsx("span",{className:"block",children:"Account"})]}),s.jsx("p",{className:"text-muted-foreground",children:"Join our community and start your Freelancing Journey."})]}),(0,s.jsxs)("div",{className:"my-4 text-center text-xs sm:text-sm",children:["Are you a business?"," ",s.jsx(_.z,{variant:"outline",size:"sm",className:"ml-2",asChild:!0,children:s.jsx(a.default,{href:"/auth/sign-up/business",children:"Register Business"})})]}),s.jsx("div",{className:"flex items-center justify-center mt-4 sm:mt-8 px-2 sm:px-0",children:t.map((r,a)=>(0,s.jsxs)(o().Fragment,{children:[(0,s.jsxs)("div",{className:"relative",children:[s.jsx("div",{className:`w-8 h-8 sm:w-12 sm:h-12 flex items-center justify-center rounded-full border-2 transition-all duration-300
                ${e>r.id?"bg-primary border-primary":e===r.id?"border-primary bg-background text-primary":"border-muted bg-background text-muted"}`,children:e>r.id?s.jsx(f.Z,{className:"w-4 h-4 sm:w-6 sm:h-6 text-background"}):s.jsx(r.icon,{className:"w-4 h-4 sm:w-6 sm:h-6"})}),s.jsx("span",{className:`absolute -bottom-6 left-1/2 -translate-x-1/2 text-xs sm:text-sm whitespace-nowrap font-medium
                ${e>=r.id?"text-primary":"text-muted-foreground"}`,children:r.title})]}),a<t.length-1&&s.jsx("div",{className:"w-20 sm:w-40 mx-2 sm:mx-4 h-[2px] bg-muted",children:s.jsx("div",{className:"h-full bg-primary transition-all duration-500",style:{width:e>r.id?"100%":"0%"}})})]},r.id))})]})},L=(e,t)=>e.getFullYear()-t.getFullYear()-(e<new Date(e.getFullYear(),t.getMonth(),t.getDate())?1:0),G=c.z.object({firstName:c.z.string().min(2,{message:"First Name must be at least 2 characters."}),lastName:c.z.string().min(2,{message:"Last Name must be at least 2 characters."}),email:c.z.string().email({message:"Email must be a valid email address."}),userName:c.z.string().min(4,{message:"Username must be at least 4 characters long"}).max(20,{message:"Username must be less than 20 characters long"}).regex(/^[a-zA-Z0-9]{4}[a-zA-Z0-9_]*$/,{message:"Underscore allowed only after 4 letters/numbers"}),phone:c.z.string().min(10,{message:"Phone number must be at least 10 digits."}).regex(/^\d+$/,{message:"Phone number can only contain digits."}),githubLink:c.z.string().optional().refine(e=>!e||/^https?:\/\/(www\.)?github\.com\/[a-zA-Z0-9_-]+\/?$/.test(e),{message:'GitHub URL must start with "https://github.com/" or "www.github.com/" and have a valid username'}),linkedin:c.z.string().optional().refine(e=>!e||/^https:\/\/www\.linkedin\.com\/in\/[a-zA-Z0-9_-]+\/?$/.test(e),{message:'LinkedIn URL must start with "https://www.linkedin.com/in/" and have a valid username'}),personalWebsite:c.z.string().optional().refine(e=>!e||/^(https?:\/\/|www\.)[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}.*[a-zA-Z0-9].*$/.test(e),{message:'Invalid website URL. Must start with "www." or "https://" and contain letters'}),password:c.z.string().min(6,{message:"Password must be at least 6 characters."}),perHourPrice:c.z.number().max(300,"Per hour price must not excedd 300").refine(e=>e>=0,{message:"Price must be a non-negative number."}),referralCode:c.z.string().optional(),workExperience:c.z.number().min(0,"Work experience must be at least 0 years").max(60,"Work experience must not exceed 60 years"),dob:c.z.union([c.z.string(),c.z.date()]).optional().refine(e=>{if(!e)return!0;let t=new Date(e),r=new Date,s=new Date;return s.setFullYear(r.getFullYear()-16),t<=s},{message:"You must be at least 16 years old"}),confirmPassword:c.z.string().min(6,"Confirm Password must be at least 6 characters long")}).refine(e=>e.password===e.confirmPassword,{path:["confirmPassword"],message:"Passwords do not match"}).refine(e=>{if(!e.dob)return!0;let t=new Date(e.dob),r=L(new Date,t);return e.workExperience<=r},{path:["workExperience"],message:"Work experience cannot be greater than your age"});function B(){let[e,t]=(0,i.useState)(0);return s.jsx("div",{className:"flex w-full items-center justify-center",children:(0,s.jsxs)("div",{className:"w-full max-w-5xl px-4 sm:px-6 lg:px-4",children:[s.jsx($,{currentStep:e}),s.jsx("div",{className:"flex justify-center w-full",children:s.jsx("div",{className:"w-full max-w-4xl",children:s.jsx(H,{currentStep:e,setCurrentStep:t})})})]})})}function H({currentStep:e,setCurrentStep:t}){let[r,a]=(0,i.useState)(!1),[n,o]=(0,i.useState)(!1),[c,p]=(0,i.useState)("IN"),[h,x]=(0,i.useState)(""),[f,S]=(0,i.useState)(!1),[q,E]=(0,i.useState)(!1),[M,O]=(0,i.useState)(!1),V=(0,l.useSearchParams)(),[I,Y]=(0,i.useState)(!1),[T,$]=(0,i.useState)(null),L=()=>{o(e=>!e)},B=(0,u.cI)({resolver:(0,m.F)(G),defaultValues:{firstName:"",lastName:"",email:"",userName:"",phone:"",githubLink:"",linkedin:"",personalWebsite:"",password:"",perHourPrice:0,workExperience:0,referralCode:"",dob:""},mode:"all"}),H=async()=>{t(e-1)},J=async()=>{if(0===e)await B.trigger(["firstName","lastName","email","dob","userName","password","confirmPassword"])?t(e+1):(0,D.Am)({variant:"destructive",title:"Validation Error",description:"Please fill in all required fields before proceeding."});else if(1===e){if(await B.trigger(["githubLink","linkedin","personalWebsite","perHourPrice","workExperience"])){let{userName:r}=B.getValues();O(!0);try{if(r===T){t(e+1);return}let s=await z.b.get(`/public/username/check-duplicate?username=${r}&is_freelancer=true`);!1===s.data.duplicate?t(e+1):((0,D.Am)({variant:"destructive",title:"User Already Exists",description:"This username is already taken. Please choose another one."}),$(r))}catch(e){(0,D.Am)({variant:"destructive",title:"API Error",description:"There was an error while checking the username."})}finally{O(!1)}}else(0,D.Am)({variant:"destructive",title:"Validation Error",description:"Please fill in all required fields before proceeding."})}},K=async e=>{let t=V.get("referral"),r=e.referralCode,n=t||r||null;x(`${N.find(e=>e.code===c)?.dialCode}${e.phone}`),a(!0);let i={...e,phone:`${N.find(e=>e.code===c)?.dialCode}${e.phone}`,phoneVerify:!1,role:"freelancer",connects:0,professionalInfo:{},skills:[],domain:[],education:{},projects:{},isFreelancer:!0,refer:{name:"string",contact:"string"},pendingProject:[],rejectedProject:[],acceptedProject:[],oracleProject:[],userDataForVerification:[],interviewsAligned:[],dob:e.dob?new Date(e.dob).toISOString():null},o=n?`/register/freelancer?referralCode=${n}`:"/register/freelancer";try{await z.b.post(o,i),(0,D.Am)({title:"Account created successfully!",description:"Redirecting to login page..."}),S(!0)}catch(t){let e=t.response?.data?.message||"Something went wrong!";console.error("API Error:",t),(0,D.Am)({variant:"destructive",title:"Uh oh! Something went wrong.",description:e,action:s.jsx(d.gD,{altText:"Try again",children:"Try again"})})}finally{setTimeout(()=>a(!1),100)}};return s.jsx(Z.l0,{...B,children:s.jsx("form",{onSubmit:B.handleSubmit(K),className:"w-full max-w-3xl mx-auto",children:s.jsx("div",{className:"w-full p-4 sm:p-6 rounded-lg shadow-sm border",children:(0,s.jsxs)("div",{className:"grid gap-4 sm:gap-6 w-full",children:[(0,s.jsxs)("div",{className:(0,P.cn)("grid gap-4",0===e?"":"hidden"),children:[(0,s.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[s.jsx(k.Z,{control:B.control,name:"firstName",label:"First Name",placeholder:"Max",className:"w-full"}),s.jsx(k.Z,{control:B.control,name:"lastName",label:"Last Name",placeholder:"Robinson",className:"w-full"})]}),(0,s.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[s.jsx(k.Z,{control:B.control,name:"email",label:"Email",placeholder:"<EMAIL>",type:"email"}),(0,s.jsxs)("div",{className:"flex flex-col gap-2 mt-1",children:[s.jsx(A.Label,{className:"text-sm font-medium",children:"Date of Birth"}),s.jsx(u.Qr,{control:B.control,name:"dob",render:({field:e})=>s.jsx(W,{field:e})})]})]}),s.jsx(k.Z,{control:B.control,name:"userName",label:"Username",placeholder:"JohnDoe123"}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(A.Label,{children:"Password"}),s.jsx(Z.Wi,{control:B.control,name:"password",render:({field:e})=>(0,s.jsxs)(Z.xJ,{children:[s.jsx(Z.NI,{children:(0,s.jsxs)("div",{className:"relative",children:[s.jsx(F.I,{placeholder:"Enter your password",type:n?"text":"password",className:"pr-10",...e}),s.jsx("button",{type:"button",onClick:L,className:"absolute inset-y-0 right-0 px-3 flex items-center",children:n?s.jsx(g.Z,{className:"h-4 w-4 sm:h-5 sm:w-5"}):s.jsx(j.Z,{className:"h-4 w-4 sm:h-5 sm:w-5"})})]})}),s.jsx(Z.zG,{})]})})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(A.Label,{children:"Confirm Password"}),s.jsx(Z.Wi,{control:B.control,name:"confirmPassword",render:({field:e})=>(0,s.jsxs)(Z.xJ,{children:[s.jsx(Z.NI,{children:(0,s.jsxs)("div",{className:"relative",children:[s.jsx(F.I,{placeholder:"Confirm your password",type:n?"text":"password",className:"pr-10",...e}),s.jsx("button",{type:"button",onClick:L,className:"absolute inset-y-0 right-0 px-3 flex items-center",children:n?s.jsx(g.Z,{className:"h-4 w-4 sm:h-5 sm:w-5"}):s.jsx(j.Z,{className:"h-4 w-4 sm:h-5 sm:w-5"})})]})}),s.jsx(Z.zG,{})]})})]}),s.jsx("div",{className:"flex gap-2 justify-end mt-4",children:s.jsx(_.z,{type:"button",onClick:J,className:"w-full sm:w-auto flex items-center justify-center",disabled:M,children:M?s.jsx(b.Z,{size:20,className:"animate-spin"}):(0,s.jsxs)(s.Fragment,{children:["Next",s.jsx(w.Z,{className:"w-4 h-4 ml-2"})]})})})]}),(0,s.jsxs)("div",{className:(0,P.cn)("grid gap-4",1===e?"":"hidden"),children:[(0,s.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[s.jsx(k.Z,{control:B.control,name:"githubLink",label:"GitHub",type:"url",placeholder:"https://github.com/yourusername",className:"w-full"}),s.jsx(k.Z,{control:B.control,name:"referralCode",label:"Referral",type:"string",placeholder:"JOHN123",className:"w-full"})]}),(0,s.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[s.jsx(k.Z,{control:B.control,name:"linkedin",label:"LinkedIn",type:"url",placeholder:"https://linkedin.com/in/yourprofile",className:"w-full"}),s.jsx(k.Z,{control:B.control,name:"personalWebsite",label:"Personal Website",type:"url",placeholder:"https://www.yourwebsite.com",className:"w-full"})]}),(0,s.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[s.jsx(k.Z,{control:B.control,name:"perHourPrice",label:"Hourly Rate ($)",type:"number",placeholder:"0",className:"w-full"}),s.jsx(k.Z,{control:B.control,name:"workExperience",label:"Work Experience (Years)",type:"number",placeholder:"0",className:"w-full"})]}),(0,s.jsxs)("div",{className:"flex gap-2 justify-between mt-4",children:[(0,s.jsxs)(_.z,{type:"button",onClick:H,className:"w-full sm:w-auto",children:[s.jsx(v.Z,{className:"w-4 h-4 mr-2"}),"Previous"]}),(0,s.jsxs)(_.z,{type:"button",onClick:J,className:"w-full sm:w-auto",children:["Next",s.jsx(w.Z,{className:"w-4 h-4 ml-2"})]})]})]}),(0,s.jsxs)("div",{className:(0,P.cn)("grid gap-4",2===e?"":"hidden"),children:[(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(A.Label,{htmlFor:"phone",children:"Phone Number"}),s.jsx(C.Z,{control:B.control,setCode:p,code:c})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 mt-4",children:[s.jsx("input",{type:"checkbox",id:"terms",checked:q,onChange:()=>{I||E(!q)},className:"rounded border-gray-300 text-primary focus:ring-primary"}),(0,s.jsxs)("label",{htmlFor:"terms",className:"text-sm text-gray-600",children:["I agree to the"," ",s.jsx("span",{onClick:()=>Y(!0),className:"text-primary hover:underline",children:"Terms and Conditions"})]}),s.jsx(U,{open:I,setOpen:Y,setIsChecked:E})]}),(0,s.jsxs)("div",{className:"flex gap-2 flex-col sm:flex-row justify-between mt-4",children:[(0,s.jsxs)(_.z,{type:"button",onClick:H,className:"w-full sm:w-auto",children:[s.jsx(v.Z,{className:"w-4 h-4 mr-2"}),"Previous"]}),(0,s.jsxs)(_.z,{type:"submit",className:"w-full sm:w-auto",disabled:r||!q,children:[r?s.jsx(b.Z,{className:"mr-2 h-4 w-4 animate-spin"}):s.jsx(y.Z,{className:"mr-2 h-4 w-4"}),"Create account"]})]})]}),s.jsx(R.Z,{phoneNumber:h,isModalOpen:f,setIsModalOpen:S})]})})})})}function J(){return(0,s.jsxs)("div",{className:"relative min-h-screen",children:[s.jsx("div",{className:"absolute left-4 top-4 sm:left-10 sm:top-10",children:s.jsx(n.T,{})}),s.jsx("div",{className:"flex items-center justify-center py-20 sm:py-12",children:s.jsx("div",{className:"mx-auto w-full px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"grid gap-6",children:[s.jsx(B,{}),(0,s.jsxs)("div",{className:"mt-4 text-center text-xs sm:text-sm",children:["Already have an account?"," ",s.jsx(_.z,{variant:"outline",size:"sm",className:"ml-2",asChild:!0,children:s.jsx(a.default,{href:"/auth/login",children:"Sign in"})})]}),(0,s.jsxs)("p",{className:"px-2 text-center text-xs text-muted-foreground sm:px-8 sm:text-sm",children:["By clicking continue, you agree to our"," ",s.jsx(_.z,{variant:"link",className:"p-0",asChild:!0,children:s.jsx(a.default,{href:"/terms",children:"Terms of Service"})})," ","and"," ",s.jsx(_.z,{variant:"link",className:"p-0",asChild:!0,children:s.jsx(a.default,{href:"/privacy",children:"Privacy Policy."})})]})]})})})]})}},33194:(e,t,r)=>{"use strict";r.d(t,{f:()=>c});var s=r(10326);r(17577);var a=r(11890),n=r(39183),i=r(25579),o=r(51223),l=r(91664);function c({className:e,classNames:t,showOutsideDays:r=!0,...c}){return s.jsx(i._W,{showOutsideDays:r,className:(0,o.cn)("p-3",e),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:(0,o.cn)((0,l.d)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,o.cn)((0,l.d)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...t},components:{IconLeft:({...e})=>s.jsx(a.Z,{className:"h-4 w-4"}),IconRight:({...e})=>s.jsx(n.Z,{className:"h-4 w-4"})},...c})}c.displayName="Calendar"},51027:(e,t,r)=>{"use strict";r.d(t,{J2:()=>o,xo:()=>l,yk:()=>c});var s=r(10326),a=r(17577),n=r(78728),i=r(51223);let o=n.fC,l=n.xz,c=a.forwardRef(({className:e,align:t="center",sideOffset:r=4,...a},o)=>s.jsx(n.h_,{children:s.jsx(n.VY,{ref:o,align:t,sideOffset:r,className:(0,i.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...a})}));c.displayName=n.VY.displayName},82487:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>i,__esModule:()=>n,default:()=>o});var s=r(68570);let a=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\auth\sign-up\freelancer\page.tsx`),{__esModule:n,$$typeof:i}=a;a.default;let o=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\auth\sign-up\freelancer\page.tsx#default`)},78728:(e,t,r)=>{"use strict";r.d(t,{VY:()=>G,h_:()=>L,fC:()=>U,xz:()=>$});var s=r(17577),a=r(82561),n=r(48051),i=r(93095),o=r(825),l=r(80699),c=r(10441),d=r(88957),u=r(17103),m=r(83078),p=r(9815),h=r(77335),x=r(10326),f=s.forwardRef((e,t)=>{let{children:r,...a}=e,n=s.Children.toArray(r),i=n.find(b);if(i){let e=i.props.children,r=n.map(t=>t!==i?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,x.jsx)(g,{...a,ref:t,children:s.isValidElement(e)?s.cloneElement(e,void 0,r):null})}return(0,x.jsx)(g,{...a,ref:t,children:r})});f.displayName="Slot";var g=s.forwardRef((e,t)=>{let{children:r,...a}=e;if(s.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r);return s.cloneElement(r,{...function(e,t){let r={...t};for(let s in t){let a=e[s],n=t[s];/^on[A-Z]/.test(s)?a&&n?r[s]=(...e)=>{n(...e),a(...e)}:a&&(r[s]=a):"style"===s?r[s]={...a,...n}:"className"===s&&(r[s]=[a,n].filter(Boolean).join(" "))}return{...e,...r}}(a,r.props),ref:t?(0,n.F)(t,e):e})}return s.Children.count(r)>1?s.Children.only(null):null});g.displayName="SlotClone";var j=({children:e})=>(0,x.jsx)(x.Fragment,{children:e});function b(e){return s.isValidElement(e)&&e.type===j}var w=r(52067),v=r(35664),y=r(17397),N="Popover",[C,P]=(0,i.b)(N,[u.D7]),k=(0,u.D7)(),[_,z]=C(N),D=e=>{let{__scopePopover:t,children:r,open:a,defaultOpen:n,onOpenChange:i,modal:o=!1}=e,l=k(t),c=s.useRef(null),[m,p]=s.useState(!1),[h=!1,f]=(0,w.T)({prop:a,defaultProp:n,onChange:i});return(0,x.jsx)(u.fC,{...l,children:(0,x.jsx)(_,{scope:t,contentId:(0,d.M)(),triggerRef:c,open:h,onOpenChange:f,onOpenToggle:s.useCallback(()=>f(e=>!e),[f]),hasCustomAnchor:m,onCustomAnchorAdd:s.useCallback(()=>p(!0),[]),onCustomAnchorRemove:s.useCallback(()=>p(!1),[]),modal:o,children:r})})};D.displayName=N;var A="PopoverAnchor";s.forwardRef((e,t)=>{let{__scopePopover:r,...a}=e,n=z(A,r),i=k(r),{onCustomAnchorAdd:o,onCustomAnchorRemove:l}=n;return s.useEffect(()=>(o(),()=>l()),[o,l]),(0,x.jsx)(u.ee,{...i,...a,ref:t})}).displayName=A;var Z="PopoverTrigger",F=s.forwardRef((e,t)=>{let{__scopePopover:r,...s}=e,i=z(Z,r),o=k(r),l=(0,n.e)(t,i.triggerRef),c=(0,x.jsx)(h.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":W(i.open),...s,ref:l,onClick:(0,a.M)(e.onClick,i.onOpenToggle)});return i.hasCustomAnchor?c:(0,x.jsx)(u.ee,{asChild:!0,...o,children:c})});F.displayName=Z;var R="PopoverPortal",[S,q]=C(R,{forceMount:void 0}),E=e=>{let{__scopePopover:t,forceMount:r,children:s,container:a}=e,n=z(R,t);return(0,x.jsx)(S,{scope:t,forceMount:r,children:(0,x.jsx)(p.z,{present:r||n.open,children:(0,x.jsx)(m.h,{asChild:!0,container:a,children:s})})})};E.displayName=R;var M="PopoverContent",O=s.forwardRef((e,t)=>{let r=q(M,e.__scopePopover),{forceMount:s=r.forceMount,...a}=e,n=z(M,e.__scopePopover);return(0,x.jsx)(p.z,{present:s||n.open,children:n.modal?(0,x.jsx)(V,{...a,ref:t}):(0,x.jsx)(I,{...a,ref:t})})});O.displayName=M;var V=s.forwardRef((e,t)=>{let r=z(M,e.__scopePopover),i=s.useRef(null),o=(0,n.e)(t,i),l=s.useRef(!1);return s.useEffect(()=>{let e=i.current;if(e)return(0,v.Ry)(e)},[]),(0,x.jsx)(y.Z,{as:f,allowPinchZoom:!0,children:(0,x.jsx)(Y,{...e,ref:o,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,a.M)(e.onCloseAutoFocus,e=>{e.preventDefault(),l.current||r.triggerRef.current?.focus()}),onPointerDownOutside:(0,a.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,s=2===t.button||r;l.current=s},{checkForDefaultPrevented:!1}),onFocusOutside:(0,a.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),I=s.forwardRef((e,t)=>{let r=z(M,e.__scopePopover),a=s.useRef(!1),n=s.useRef(!1);return(0,x.jsx)(Y,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(a.current||r.triggerRef.current?.focus(),t.preventDefault()),a.current=!1,n.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(a.current=!0,"pointerdown"!==t.detail.originalEvent.type||(n.current=!0));let s=t.target;r.triggerRef.current?.contains(s)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&n.current&&t.preventDefault()}})}),Y=s.forwardRef((e,t)=>{let{__scopePopover:r,trapFocus:s,onOpenAutoFocus:a,onCloseAutoFocus:n,disableOutsidePointerEvents:i,onEscapeKeyDown:d,onPointerDownOutside:m,onFocusOutside:p,onInteractOutside:h,...f}=e,g=z(M,r),j=k(r);return(0,l.EW)(),(0,x.jsx)(c.M,{asChild:!0,loop:!0,trapped:s,onMountAutoFocus:a,onUnmountAutoFocus:n,children:(0,x.jsx)(o.XB,{asChild:!0,disableOutsidePointerEvents:i,onInteractOutside:h,onEscapeKeyDown:d,onPointerDownOutside:m,onFocusOutside:p,onDismiss:()=>g.onOpenChange(!1),children:(0,x.jsx)(u.VY,{"data-state":W(g.open),role:"dialog",id:g.contentId,...j,...f,ref:t,style:{...f.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),T="PopoverClose";function W(e){return e?"open":"closed"}s.forwardRef((e,t)=>{let{__scopePopover:r,...s}=e,n=z(T,r);return(0,x.jsx)(h.WV.button,{type:"button",...s,ref:t,onClick:(0,a.M)(e.onClick,()=>n.onOpenChange(!1))})}).displayName=T,s.forwardRef((e,t)=>{let{__scopePopover:r,...s}=e,a=k(r);return(0,x.jsx)(u.Eh,{...a,...s,ref:t})}).displayName="PopoverArrow";var U=D,$=F,L=E,G=O}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,4198,6034,4718,5645,2146,6686,4736,9169,8893],()=>r(45184));module.exports=s})();