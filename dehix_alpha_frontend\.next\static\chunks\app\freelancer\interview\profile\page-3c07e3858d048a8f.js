(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[379],{33453:function(e,t,s){Promise.resolve().then(s.bind(s,31515))},62737:function(e){var t;t=function(){"use strict";var e="millisecond",t="second",s="minute",r="hour",n="week",a="month",i="quarter",l="year",c="date",d="Invalid Date",o=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,u=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,m=function(e,t,s){var r=String(e);return!r||r.length>=t?e:""+Array(t+1-r.length).join(s)+e},h="en",x={};x[h]={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var t=["th","st","nd","rd"],s=e%100;return"["+e+(t[(s-20)%10]||t[s]||"th")+"]"}};var p="$isDayjsObject",f=function(e){return e instanceof y||!(!e||!e[p])},v=function e(t,s,r){var n;if(!t)return h;if("string"==typeof t){var a=t.toLowerCase();x[a]&&(n=a),s&&(x[a]=s,n=a);var i=t.split("-");if(!n&&i.length>1)return e(i[0])}else{var l=t.name;x[l]=t,n=l}return!r&&n&&(h=n),n||!r&&h},j=function(e,t){if(f(e))return e.clone();var s="object"==typeof t?t:{};return s.date=e,s.args=arguments,new y(s)},g={s:m,z:function(e){var t=-e.utcOffset(),s=Math.abs(t);return(t<=0?"+":"-")+m(Math.floor(s/60),2,"0")+":"+m(s%60,2,"0")},m:function e(t,s){if(t.date()<s.date())return-e(s,t);var r=12*(s.year()-t.year())+(s.month()-t.month()),n=t.clone().add(r,a),i=s-n<0,l=t.clone().add(r+(i?-1:1),a);return+(-(r+(s-n)/(i?n-l:l-n))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(d){return({M:a,y:l,w:n,d:"day",D:c,h:r,m:s,s:t,ms:e,Q:i})[d]||String(d||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}};g.l=v,g.i=f,g.w=function(e,t){return j(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})};var y=function(){function m(e){this.$L=v(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[p]=!0}var h=m.prototype;return h.parse=function(e){this.$d=function(e){var t=e.date,s=e.utc;if(null===t)return new Date(NaN);if(g.u(t))return new Date;if(t instanceof Date)return new Date(t);if("string"==typeof t&&!/Z$/i.test(t)){var r=t.match(o);if(r){var n=r[2]-1||0,a=(r[7]||"0").substring(0,3);return s?new Date(Date.UTC(r[1],n,r[3]||1,r[4]||0,r[5]||0,r[6]||0,a)):new Date(r[1],n,r[3]||1,r[4]||0,r[5]||0,r[6]||0,a)}}return new Date(t)}(e),this.init()},h.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},h.$utils=function(){return g},h.isValid=function(){return this.$d.toString()!==d},h.isSame=function(e,t){var s=j(e);return this.startOf(t)<=s&&s<=this.endOf(t)},h.isAfter=function(e,t){return j(e)<this.startOf(t)},h.isBefore=function(e,t){return this.endOf(t)<j(e)},h.$g=function(e,t,s){return g.u(e)?this[t]:this.set(s,e)},h.unix=function(){return Math.floor(this.valueOf()/1e3)},h.valueOf=function(){return this.$d.getTime()},h.startOf=function(e,i){var d=this,o=!!g.u(i)||i,u=g.p(e),m=function(e,t){var s=g.w(d.$u?Date.UTC(d.$y,t,e):new Date(d.$y,t,e),d);return o?s:s.endOf("day")},h=function(e,t){return g.w(d.toDate()[e].apply(d.toDate("s"),(o?[0,0,0,0]:[23,59,59,999]).slice(t)),d)},x=this.$W,p=this.$M,f=this.$D,v="set"+(this.$u?"UTC":"");switch(u){case l:return o?m(1,0):m(31,11);case a:return o?m(1,p):m(0,p+1);case n:var j=this.$locale().weekStart||0,y=(x<j?x+7:x)-j;return m(o?f-y:f+(6-y),p);case"day":case c:return h(v+"Hours",0);case r:return h(v+"Minutes",1);case s:return h(v+"Seconds",2);case t:return h(v+"Milliseconds",3);default:return this.clone()}},h.endOf=function(e){return this.startOf(e,!1)},h.$set=function(n,i){var d,o=g.p(n),u="set"+(this.$u?"UTC":""),m=((d={}).day=u+"Date",d[c]=u+"Date",d[a]=u+"Month",d[l]=u+"FullYear",d[r]=u+"Hours",d[s]=u+"Minutes",d[t]=u+"Seconds",d[e]=u+"Milliseconds",d)[o],h="day"===o?this.$D+(i-this.$W):i;if(o===a||o===l){var x=this.clone().set(c,1);x.$d[m](h),x.init(),this.$d=x.set(c,Math.min(this.$D,x.daysInMonth())).$d}else m&&this.$d[m](h);return this.init(),this},h.set=function(e,t){return this.clone().$set(e,t)},h.get=function(e){return this[g.p(e)]()},h.add=function(e,i){var c,d=this;e=Number(e);var o=g.p(i),u=function(t){var s=j(d);return g.w(s.date(s.date()+Math.round(t*e)),d)};if(o===a)return this.set(a,this.$M+e);if(o===l)return this.set(l,this.$y+e);if("day"===o)return u(1);if(o===n)return u(7);var m=((c={})[s]=6e4,c[r]=36e5,c[t]=1e3,c)[o]||1,h=this.$d.getTime()+e*m;return g.w(h,this)},h.subtract=function(e,t){return this.add(-1*e,t)},h.format=function(e){var t=this,s=this.$locale();if(!this.isValid())return s.invalidDate||d;var r=e||"YYYY-MM-DDTHH:mm:ssZ",n=g.z(this),a=this.$H,i=this.$m,l=this.$M,c=s.weekdays,o=s.months,m=s.meridiem,h=function(e,s,n,a){return e&&(e[s]||e(t,r))||n[s].slice(0,a)},x=function(e){return g.s(a%12||12,e,"0")},p=m||function(e,t,s){var r=e<12?"AM":"PM";return s?r.toLowerCase():r};return r.replace(u,function(e,r){return r||function(e){switch(e){case"YY":return String(t.$y).slice(-2);case"YYYY":return g.s(t.$y,4,"0");case"M":return l+1;case"MM":return g.s(l+1,2,"0");case"MMM":return h(s.monthsShort,l,o,3);case"MMMM":return h(o,l);case"D":return t.$D;case"DD":return g.s(t.$D,2,"0");case"d":return String(t.$W);case"dd":return h(s.weekdaysMin,t.$W,c,2);case"ddd":return h(s.weekdaysShort,t.$W,c,3);case"dddd":return c[t.$W];case"H":return String(a);case"HH":return g.s(a,2,"0");case"h":return x(1);case"hh":return x(2);case"a":return p(a,i,!0);case"A":return p(a,i,!1);case"m":return String(i);case"mm":return g.s(i,2,"0");case"s":return String(t.$s);case"ss":return g.s(t.$s,2,"0");case"SSS":return g.s(t.$ms,3,"0");case"Z":return n}return null}(e)||n.replace(":","")})},h.utcOffset=function(){return-(15*Math.round(this.$d.getTimezoneOffset()/15))},h.diff=function(e,c,d){var o,u=this,m=g.p(c),h=j(e),x=(h.utcOffset()-this.utcOffset())*6e4,p=this-h,f=function(){return g.m(u,h)};switch(m){case l:o=f()/12;break;case a:o=f();break;case i:o=f()/3;break;case n:o=(p-x)/6048e5;break;case"day":o=(p-x)/864e5;break;case r:o=p/36e5;break;case s:o=p/6e4;break;case t:o=p/1e3;break;default:o=p}return d?o:g.a(o)},h.daysInMonth=function(){return this.endOf(a).$D},h.$locale=function(){return x[this.$L]},h.locale=function(e,t){if(!e)return this.$L;var s=this.clone(),r=v(e,t,!0);return r&&(s.$L=r),s},h.clone=function(){return g.w(this.$d,this)},h.toDate=function(){return new Date(this.valueOf())},h.toJSON=function(){return this.isValid()?this.toISOString():null},h.toISOString=function(){return this.$d.toISOString()},h.toString=function(){return this.$d.toUTCString()},m}(),N=y.prototype;return j.prototype=N,[["$ms",e],["$s",t],["$m",s],["$H",r],["$W","day"],["$M",a],["$y",l],["$D",c]].forEach(function(e){N[e[1]]=function(t){return this.$g(t,e[0],e[1])}}),j.extend=function(e,t){return e.$i||(e(t,y,j),e.$i=!0),j},j.locale=v,j.isDayjs=f,j.unix=function(e){return j(1e3*e)},j.en=x[h],j.Ls=x,j.p={},j},e.exports=t()},25912:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(33480).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},30690:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(33480).Z)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},31515:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return V}});var r=s(57437),n=s(2265),a=s(11444),i=s(62688),l=s(64797),c=s(59282),d=s(39343),o=s(31014),u=s(59772),m=s(72377),h=s(92513),x=s(30690);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let p=(0,s(33480).Z)("Pen",[["path",{d:"M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z",key:"5qss01"}]]);var f=s(78068),v=s(89733),j=s(47304),g=s(15922),y=s(2183),N=s(79055);function b(e){let{onClick:t,variant:s="ghost",icon:n,...a}=e;return(0,r.jsx)(v.z,{variant:s,size:"icon",className:"h-4 py-4",onClick:t,...a,children:n})}var w=s(54662),S=s(2128),$=s(77209);let k=u.z.object({name:u.z.string().min(1,"Domain is required"),experience:u.z.preprocess(e=>parseFloat(e),u.z.number().min(0,"Experience must be a non-negative number").max(50,"Experience can't exceed 50")),level:u.z.string().min(1,"Level is required")});var D=e=>{let{open:t,onClose:s,onSubmit:n,domainOptions:a,levels:i,defaultValues:l,loading:c}=e,{handleSubmit:u,control:m,reset:h,formState:{errors:x}}=(0,d.cI)({resolver:(0,o.F)(k),defaultValues:l});return(0,r.jsx)(w.Vq,{open:t,onOpenChange:s,children:(0,r.jsxs)(w.cZ,{children:[(0,r.jsxs)(w.fK,{children:[(0,r.jsx)(w.$N,{children:l?"Edit Domain":"Add Domain"}),(0,r.jsx)(w.Be,{children:"Select a domain and provide your experience and level."})]}),(0,r.jsxs)("form",{onSubmit:u(e=>{n(e),h(),s()}),children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(d.Qr,{control:m,name:"name",render:e=>{let{field:t}=e;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(S.Ph,{onValueChange:t.onChange,value:t.value,children:[(0,r.jsx)(S.i4,{className:"w-full",children:(0,r.jsx)(S.ki,{placeholder:"Select a domain"})}),(0,r.jsx)(S.Bw,{children:a.length>0?a.map((e,t)=>(0,r.jsx)(S.Ql,{value:e.talentName,children:e.talentName},t)):(0,r.jsxs)("p",{className:"p-2",children:["No verified Domain."," ",(0,r.jsx)("span",{className:"text-blue-500",children:"Get verified !"})]})})]}),x.name&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:x.name.message})]})}}),(0,r.jsx)(d.Qr,{control:m,name:"experience",render:e=>{let{field:t}=e;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"col-span-3 relative",children:[(0,r.jsx)($.I,{...t,placeholder:"Years of experience",type:"number",min:"0",step:"0.1",className:"w-full pl-2 pr-1"}),(0,r.jsx)("span",{className:"absolute right-8 top-1/2 transform -translate-y-1/2 text-grey-500 pointer-events-none",children:"YEARS"})]}),x.experience&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:x.experience.message})]})}}),(0,r.jsx)(d.Qr,{control:m,name:"level",render:e=>{let{field:t}=e;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(S.Ph,{onValueChange:t.onChange,value:t.value,children:[(0,r.jsx)(S.i4,{children:(0,r.jsx)(S.ki,{placeholder:"Select level"})}),(0,r.jsx)(S.Bw,{children:i.map((e,t)=>(0,r.jsx)(S.Ql,{value:e,children:e},t))})]}),x.level&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:x.level.message})]})}})]}),(0,r.jsx)(w.cN,{children:(0,r.jsxs)(v.z,{className:"mt-3",type:"submit",disabled:c,children:[l?"Update":"Add"," Domain"]})})]})]})})},M=s(89859);let Y=u.z.object({name:u.z.string().min(1,"Skill is required"),experience:u.z.preprocess(e=>parseFloat(e),u.z.number().min(0,"Experience must be a non-negative number").max(50,"Experience can't exceed 50")),level:u.z.string().min(1,"Level is required")});var O=e=>{let{open:t,onClose:s,onSubmit:n,skillOptions:a,levels:i,defaultValues:l,loading:c}=e,{handleSubmit:u,control:m,reset:h,formState:{errors:x}}=(0,d.cI)({resolver:(0,o.F)(Y),defaultValues:l});return(0,r.jsx)(w.Vq,{open:t,onOpenChange:s,children:(0,r.jsxs)(w.cZ,{children:[(0,r.jsxs)(w.fK,{children:[(0,r.jsx)(w.$N,{children:l?"Edit Skill":"Add Skill"}),(0,r.jsx)(w.Be,{children:"Select a skill and provide your experience and level."})]}),(0,r.jsxs)("form",{onSubmit:u(e=>{n(e),h(),s()}),children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(d.Qr,{control:m,name:"name",render:e=>{let{field:t}=e;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(S.Ph,{onValueChange:t.onChange,value:t.value,children:[(0,r.jsx)(S.i4,{className:"w-full",children:(0,r.jsx)(S.ki,{placeholder:"Select a skill"})}),(0,r.jsx)(S.Bw,{children:a.length>0?a.map((e,t)=>(0,r.jsx)(S.Ql,{value:e.talentName,children:e.talentName},t)):(0,r.jsxs)("p",{className:"p-2",children:["No verified skills."," ",(0,r.jsx)("span",{className:"text-blue-500",children:"Get verified !"})]})})]}),x.name&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:x.name.message})]})}}),(0,r.jsx)(d.Qr,{control:m,name:"experience",render:e=>{let{field:t}=e;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"col-span-3 relative",children:[(0,r.jsx)($.I,{...t,placeholder:"Years of experience",type:"number",min:"0",step:"0.1",className:"w-full pl-2 pr-1"}),(0,r.jsx)("span",{className:"absolute right-8 top-1/2 transform -translate-y-1/2 text-grey-500 pointer-events-none",children:"YEARS"})]}),x.experience&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:x.experience.message})]})}}),(0,r.jsx)(d.Qr,{control:m,name:"level",render:e=>{let{field:t}=e;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(S.Ph,{onValueChange:t.onChange,value:t.value,children:[(0,r.jsx)(S.i4,{children:(0,r.jsx)(S.ki,{placeholder:"Select level"})}),(0,r.jsx)(S.Bw,{children:i.map((e,t)=>(0,r.jsx)(S.Ql,{value:e,children:e},t))})]}),x.level&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:x.level.message})]})}})]}),(0,r.jsx)(w.cN,{children:(0,r.jsxs)(v.z,{className:"mt-3",type:"submit",disabled:c,children:[l?"Update":"Add"," Skill"]})})]})]})})},C=s(62737),A=s.n(C),I=s(16463),_=s(70402),F=s(86763),z=function(e){let{isOpen:t,onClose:s,doc_id:i,doc_type:l}=e,c=(0,a.v9)(e=>e.user),d=(0,I.useRouter)(),o=(0,I.useSearchParams)(),[u,m]=(0,n.useState)(""),[h,x]=(0,n.useState)(""),[p,f]=(0,n.useState)(A()().add(1,"day").format("YYYY-MM-DD")),[j,y]=(0,n.useState)(A()().add(1,"day").add(1,"hour").format("YYYY-MM-DD")),N=(0,n.useState)([""]),[b,S]=(0,n.useState)([]),k=async e=>{let t=Object.fromEntries(o.entries());t.code?await D(e,t.code):M()},D=async(e,t)=>{await g.b.post("/meeting",e,{params:{code:t}})},M=async()=>{try{let e=window.location.origin+window.location.pathname,t=(await g.b.get("/meeting/auth-url",{params:{redirectUri:e}})).data.url;t&&d.push(t)}catch(e){console.error("Error fetching Google Auth URL:",e),(0,F.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}};(0,n.useEffect)(()=>{!async function(){try{var e;let t=await g.b.get("/freelancer/".concat(c.uid,"/doc_id/").concat(i,"?doc_type=").concat(l));S(null==t?void 0:null===(e=t.data)||void 0===e?void 0:e.data)}catch(e){console.error("Error fetching data:",e),(0,F.Am)({variant:"destructive",title:"Error",description:"Something went wrong.Please try again."})}}()},[i,l,c.uid]);let Y=(e,t,s)=>{let r=A()(t).set("hour",parseInt(s.split(":")[0])).set("minute",parseInt(s.split(":")[1])).format("YYYY-MM-DDTHH:mm");"start"===e?f(r):y(r)},O=e=>{A()(e).isBefore(A()(p))?y(A()(p).add(1,"hour").format("YYYY-MM-DDTHH:mm")):y(e)};return(0,r.jsx)(w.Vq,{open:t,onOpenChange:s,children:(0,r.jsxs)(w.cZ,{className:"sm:max-w-[425px]",children:[(0,r.jsxs)(w.fK,{children:[(0,r.jsx)(w.$N,{children:"Create a Meeting"}),(0,r.jsx)(w.Be,{children:"Fill in the details below to schedule a new meeting."})]}),(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),k({summary:u,description:h,start:{dateTime:A()(p).toISOString(),timeZone:"Asia/Kolkata"},end:{dateTime:A()(j).toISOString(),timeZone:"Asia/Kolkata"},attendees:N})},className:"grid gap-4 py-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,r.jsx)(_.Label,{htmlFor:"summary",className:"text-right",children:"Summary"}),(0,r.jsx)($.I,{id:"summary",value:u,onChange:e=>m(e.target.value),className:"col-span-3",placeholder:"Meeting Summary",required:!0})]}),(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,r.jsx)(_.Label,{htmlFor:"description",className:"text-right",children:"Description"}),(0,r.jsx)($.I,{id:"description",value:h,onChange:e=>x(e.target.value),className:"col-span-3",placeholder:"Meeting Description",required:!0})]}),(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,r.jsx)(_.Label,{htmlFor:"start-date",className:"text-right",children:"Start Date"}),(0,r.jsx)($.I,{type:"date",value:A()(p).format("YYYY-MM-DD"),onChange:e=>f(e.target.value+"T"+A()(p).format("HH:mm")),className:"col-span-3",required:!0})]}),(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,r.jsx)(_.Label,{htmlFor:"end-date",className:"text-right",children:"End Date"}),(0,r.jsx)($.I,{type:"date",value:A()(j).format("YYYY-MM-DD"),onChange:e=>O(e.target.value+"T"+A()(j).format("HH:mm")),className:"col-span-3",required:!0})]}),(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,r.jsx)(_.Label,{htmlFor:"start-time",className:"text-right",children:"Start Time"}),(0,r.jsx)($.I,{type:"time",className:"col-span-3",value:A()(p).format("HH:mm"),onChange:e=>Y("start",A()(p).format("YYYY-MM-DD"),e.target.value),required:!0})]}),(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,r.jsx)(_.Label,{htmlFor:"end-time",className:"text-right",children:"End Time"}),(0,r.jsx)($.I,{type:"time",className:"col-span-3",value:A()(j).format("HH:mm"),onChange:e=>Y("end",A()(j).format("YYYY-MM-DD"),e.target.value),required:!0})]}),(0,r.jsx)(w.cN,{className:"flex justify-center",children:(0,r.jsx)(v.z,{type:"submit",children:"Create Meeting"})})]})]})})},E=s(31590),H=s(21413);let L=["Mastery","Proficient","Beginner"],Z="Pending",T=u.z.object({skill:u.z.string().min(1,"Skill is required"),experience:u.z.preprocess(e=>parseFloat(e),u.z.number().min(0,"Experience must be a non-negative number").max(50,"Experience can't exceed 50")),level:u.z.string().min(1,"Level is required")}),q=u.z.object({domain:u.z.string().min(1,"Domain is required"),experience:u.z.preprocess(e=>parseFloat(e),u.z.number().min(0,"Experience must be a non-negative number").max(50,"Experience can't exceed 50")),level:u.z.string().min(1,"Level is required")});var P=e=>{let{freelancerId:t}=e,s=(0,a.v9)(e=>e.user),[i,l]=(0,n.useState)([]),[c,u]=(0,n.useState)([]),[w,S]=(0,n.useState)([]),[$,k]=(0,n.useState)([]),[Y,C]=(0,n.useState)(!1),[A,I]=(0,n.useState)(!1),[_,F]=(0,n.useState)(!1),[P,V]=(0,n.useState)(null),[B,U]=(0,n.useState)(null),[Q,W]=(0,n.useState)(!1),[R,J]=(0,n.useState)(),[K,G]=(0,n.useState)(),[X,ee]=n.useState("All");(0,n.useEffect)(()=>{!async function(){C(!0);try{let e=await g.b.get("/freelancer/".concat(s.uid,"/skill")),r=await g.b.get("/freelancer/".concat(t,"/domain")),n=e.data.data[0].skills,a=r.data.data[0].domain;S(n),k(a);let i=await g.b.get("/freelancer/".concat(s.uid,"/dehix-talent")),c=i.data.data.skills.filter(e=>!n.some(t=>t.name===e.talentName));l(c);let d=i.data.data.domains.filter(e=>!a.some(t=>t.name===e.talentName));u(d)}catch(e){console.error("Error fetching data:",e),(0,f.Am)({variant:"destructive",title:"Error",description:"Failed to fetch data. Please try again later."})}finally{C(!1)}}()},[t,null==s?void 0:s.uid]);let{reset:et}=(0,d.cI)({resolver:(0,o.F)(T)}),{reset:es}=(0,d.cI)({resolver:(0,o.F)(q)}),er=async e=>{C(!0);try{let t={skills:[{name:e.name,level:e.level,experience:e.experience,interviewPermission:"NOT_VERIFIED"}]};if(P){let s={...P,...e,interviewStatus:Z},r=await g.b.put("/freelancer/skill",t);if(200===r.status){let t=w.map(e=>e._id===P._id?{...e,...s}:e);S(t),(0,f.Am)({title:"Skill Updated",description:"".concat(e.name," skill updated successfully.")})}else throw Error("Failed to update skill")}else{let s=await g.b.put("/freelancer/skill",t);if(200===s.status)S([...w,{name:e.name,experience:e.experience,level:e.level,interviewStatus:Z}]),(0,f.Am)({title:"Skill Added",description:"".concat(e.name," skill added successfully.")});else throw Error("Failed to add skill");S([...w,{name:e.name,experience:e.experience,level:e.level,interviewStatus:Z}]),(0,f.Am)({title:"Skill Added",description:"".concat(e.name," skill added successfully.")})}et(),I(!1)}finally{C(!1)}},en=async e=>{C(!0);try{if(B){let t={...B,...e},s=await g.b.put("/freelancer/domain",t);if(200===s.status){let s=$.map(e=>e._id===B._id?{...e,...t}:e);k(s),(0,f.Am)({title:"Domain Updated",description:"".concat(e.name," domain updated successfully.")})}else throw Error("Failed to update domain")}else k([...$,{name:e.name,experience:e.experience,level:e.level,interviewStatus:Z}]),(0,f.Am)({title:"Domain Added",description:"".concat(e.name," domain added successfully.")});es(),F(!1)}finally{C(!1)}},ea=(e,t)=>{console.log("Opening dialog for",t,"with ID:",null==e?void 0:e._id),W(!0),J(null==e?void 0:e._id),G(t)};return(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"mb-8 ml-5",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"Interview Profile"}),(0,r.jsx)("p",{className:"text-gray-400 mt-2",children:"Manage and track your skills and domains. Add new skills or domains and provide your experience levels."})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-4 p-2 sm:px-6 sm:py-0 md:gap-8  pt-2 pl-4 sm:pt-4 sm:pl-6 md:pt-6 md:pl-8 min-h-screen relative",children:[(0,r.jsx)("div",{className:"w-1/5",children:(0,r.jsxs)(E.h_,{children:[(0,r.jsx)(E.$F,{asChild:!0,children:(0,r.jsxs)(v.z,{variant:"outline",size:"sm",className:"h-7 gap-1 w-auto text-sm",children:[(0,r.jsx)(m.Z,{className:"h-3.5 w-3.5"}),(0,r.jsx)("span",{className:"sr-only sm:not-sr-only",children:"Filter"})]})}),(0,r.jsxs)(E.AW,{align:"end",children:[(0,r.jsx)(E.Ju,{children:"Filter by"}),(0,r.jsx)(E.VD,{}),(0,r.jsx)(E.bO,{checked:"All"===X,onSelect:()=>ee("All"),children:"All"}),(0,r.jsx)(E.bO,{checked:"Skills"===X,onSelect:()=>ee("Skills"),children:"Skills"}),(0,r.jsx)(E.bO,{checked:"Domain"===X,onSelect:()=>ee("Domain"),children:"Domain"})]})]})}),(0,r.jsxs)("div",{className:"w-full relative border border-gray-200 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h2",{className:"text-xs md:text-xl font-semibold w-1/2",children:"Skills & Domains"}),(0,r.jsxs)("div",{className:"flex justify-end items-center  w-1/2",children:[(0,r.jsxs)(v.z,{onClick:()=>I(!0),className:"mr-2 md:px-4 md:py-2 py-1 px-1.5 text-xs md:text-sm",children:[(0,r.jsx)(h.Z,{className:" mr-1 md:mr-2 h-4 w-4"})," ",(0,r.jsx)("span",{className:"hidden  md:block mr-1",children:"Add"})," Skill"]}),(0,r.jsxs)(v.z,{onClick:()=>F(!0),className:"mr-2 md:px-4 md:py-2 py-1 px-1.5 text-xs md:text-sm",children:[(0,r.jsx)(h.Z,{className:" mr-1 md:mr-2 h-4 w-4"})," ",(0,r.jsx)("span",{className:"hidden md:block mr-1",children:"Add"})," Domain"]})]}),(0,r.jsx)(O,{open:A,onClose:()=>I(!1),onSubmit:er,skillOptions:i,levels:L,defaultValues:P||void 0,loading:Y}),(0,r.jsx)(D,{open:_,onClose:()=>F(!1),onSubmit:en,domainOptions:c,levels:L,defaultValues:B||void 0,loading:Y})]}),(0,r.jsxs)(j.iA,{children:[(0,r.jsx)(j.xD,{children:(0,r.jsxs)(j.SC,{className:"hover:bg-[#09090B]",children:[(0,r.jsx)(j.ss,{className:"",children:"Item"}),(0,r.jsx)(j.ss,{className:"",children:"Level"}),(0,r.jsx)(j.ss,{className:"",children:"Experience"}),(0,r.jsx)(j.ss,{className:"",children:"Status"}),(0,r.jsx)(j.ss,{className:"",children:(0,r.jsxs)("div",{className:"flex gap-2 items-center",children:["Actions",(0,r.jsxs)(H.J2,{children:[(0,r.jsx)(H.xo,{asChild:!0,children:(0,r.jsx)(x.Z,{className:"w-4 h-4 text-muted-foreground  cursor-pointer"})}),(0,r.jsx)(H.yk,{className:"text-sm w-fit  border rounded p-2 shadow",children:"This will be available in the next phase."})]})]})})]})}),(0,r.jsx)(j.RM,{children:Y?[void 0,void 0,void 0,void 0].map((e,t)=>(0,r.jsxs)(j.SC,{children:[(0,r.jsx)(j.pj,{className:"",children:(0,r.jsx)(y.O,{className:"h-6 w-24"})}),(0,r.jsx)(j.pj,{className:"",children:(0,r.jsx)(y.O,{className:"h-6 w-24"})}),(0,r.jsx)(j.pj,{className:"",children:(0,r.jsx)(y.O,{className:"h-6 w-24"})}),(0,r.jsx)(j.pj,{className:"",children:(0,r.jsx)(y.O,{className:"h-6 w-20 rounded-full"})}),(0,r.jsx)(j.pj,{className:"",children:(0,r.jsx)(y.O,{className:"w-8 h-8 p-2 rounded-md"})})]},t)):("All"===X?[...w,...$]:"Skills"===X?w:"Domain"===X?$:void 0).map(e=>{var t;return(0,r.jsxs)(j.SC,{children:[(0,r.jsx)(j.pj,{className:"",children:e.name}),(0,r.jsx)(j.pj,{className:"",children:e.level}),(0,r.jsx)(j.pj,{className:"",children:"number"==typeof e.experience&&e.experience>0?e.experience+" years":""}),(0,r.jsx)(j.pj,{className:"",children:(0,r.jsx)(N.C,{className:(0,M.S)(e.interviewStatus),children:null==e?void 0:null===(t=e.interviewStatus)||void 0===t?void 0:t.toUpperCase()})}),(0,r.jsx)(j.pj,{className:"flex items-center gap-2",children:(0,r.jsx)(b,{icon:(0,r.jsx)(p,{className:"w-4 h-4 text-gray-400 cursor-not-allowed"}),disabled:!0,onClick:()=>ea(e,e.experience?"SKILL":"DOMAIN")})})]},e._id)})})]})]})]}),(0,r.jsx)(z,{isOpen:Q,onClose:()=>W(!1),doc_id:R||"",doc_type:K||""})]})};function V(){let e=(0,a.v9)(e=>e.user);return(0,r.jsxs)("div",{className:"flex min-h-screen w-full",children:[(0,r.jsx)(l.Z,{menuItemsTop:c.y,menuItemsBottom:c.$,active:"Profile"}),(0,r.jsxs)("div",{className:"flex flex-col sm:py-2 sm:pl-14 mb-8 w-full",children:[(0,r.jsx)(i.Z,{menuItemsTop:c.y,menuItemsBottom:c.$,activeMenu:"Dashboard",breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Interview",link:"#"}]}),(0,r.jsx)(P,{freelancerId:null==e?void 0:e.uid})]})]})}},70402:function(e,t,s){"use strict";s.r(t),s.d(t,{Label:function(){return d}});var r=s(57437),n=s(2265),a=s(38364),i=s(12218),l=s(49354);let c=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)(a.f,{ref:t,className:(0,l.cn)(c(),s),...n})});d.displayName=a.f.displayName},2183:function(e,t,s){"use strict";s.d(t,{O:function(){return a}});var r=s(57437),n=s(49354);function a(e){let{className:t,...s}=e;return(0,r.jsx)("div",{className:(0,n.cn)("animate-pulse rounded-md bg-primary/10",t),...s})}},59282:function(e,t,s){"use strict";s.d(t,{$:function(){return h},y:function(){return m}});var r=s(57437),n=s(11005),a=s(38133),i=s(33480);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,i.Z)("ListVideo",[["path",{d:"M12 12H3",key:"18klou"}],["path",{d:"M16 6H3",key:"1wxfjs"}],["path",{d:"M12 18H3",key:"11ftsu"}],["path",{d:"m16 12 5 3-5 3v-6Z",key:"zpskkp"}]]);var c=s(25912);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let d=(0,i.Z)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]);var o=s(24258),u=s(66648);let m=[{href:"#",icon:(0,r.jsx)(u.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:(0,r.jsx)(n.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/freelancer/interview/profile",icon:(0,r.jsx)(a.Z,{className:"h-5 w-5"}),label:"Profile"},{href:"/freelancer/interview/current",icon:(0,r.jsx)(l,{className:"h-5 w-5"}),label:"Current"},{href:"/freelancer/interview/bids",icon:(0,r.jsx)(c.Z,{className:"h-5 w-5"}),label:"Bids"},{href:"/freelancer/interview/history",icon:(0,r.jsx)(d,{className:"h-5 w-5"}),label:"History"}],h=[{href:"/freelancer/settings/personal-info",icon:(0,r.jsx)(o.Z,{className:"h-5 w-5"}),label:"Settings"}]},38364:function(e,t,s){"use strict";s.d(t,{f:function(){return l}});var r=s(2265),n=s(18676),a=s(57437),i=r.forwardRef((e,t)=>(0,a.jsx)(n.WV.label,{...e,ref:t,onMouseDown:t=>{var s;t.target.closest("button, input, select, textarea")||(null===(s=e.onMouseDown)||void 0===s||s.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var l=i}},function(e){e.O(0,[4358,7481,9208,9668,9227,6103,7374,1444,6648,9812,364,7715,1974,4022,7356,4046,6966,1374,2455,9726,2688,2971,7023,1744],function(){return e(e.s=33453)}),_N_E=e.O()}]);