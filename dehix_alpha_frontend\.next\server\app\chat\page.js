(()=>{var e={};e.id=1929,e.ids=[1929],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},83122:e=>{"use strict";e.exports=require("undici")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},62730:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>l.a,__next_app__:()=>p,originalPathname:()=>d,pages:()=>u,routeModule:()=>h,tree:()=>c}),n(34356),n(54302),n(12523);var r=n(23191),i=n(88716),o=n(37922),l=n.n(o),a=n(95231),s={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>a[e]);n.d(t,s);let c=["",{children:["chat",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,34356)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\chat\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(n.bind(n,54302)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.bind(n,12523)),"C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["C:\\Users\\<USER>\\Documents\\Dehix\\dehix_alpha_frontend\\src\\app\\chat\\page.tsx"],d="/chat/page",p={require:n,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/chat/page",pathname:"/chat",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},30600:(e,t,n)=>{Promise.resolve().then(n.bind(n,10180))},71771:e=>{"use strict";var t=Object.prototype.hasOwnProperty,n=Object.prototype.toString,r=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===n.call(e)},l=function(e){if(!e||"[object Object]"!==n.call(e))return!1;var r,i=t.call(e,"constructor"),o=e.constructor&&e.constructor.prototype&&t.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!i&&!o)return!1;for(r in e);return void 0===r||t.call(e,r)},a=function(e,t){r&&"__proto__"===t.name?r(e,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):e[t.name]=t.newValue},s=function(e,n){if("__proto__"===n){if(!t.call(e,n))return;if(i)return i(e,n).value}return e[n]};e.exports=function e(){var t,n,r,i,c,u,d=arguments[0],p=1,h=arguments.length,f=!1;for("boolean"==typeof d&&(f=d,d=arguments[1]||{},p=2),(null==d||"object"!=typeof d&&"function"!=typeof d)&&(d={});p<h;++p)if(t=arguments[p],null!=t)for(n in t)r=s(d,n),d!==(i=s(t,n))&&(f&&i&&(l(i)||(c=o(i)))?(c?(c=!1,u=r&&o(r)?r:[]):u=r&&l(r)?r:{},a(d,{name:n,newValue:e(f,u,i)})):void 0!==i&&a(d,{name:n,newValue:i}));return d}},6007:e=>{var t=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,n=/\n/g,r=/^\s*/,i=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,o=/^:\s*/,l=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,a=/^[;\s]*/,s=/^\s+|\s+$/g;function c(e){return e?e.replace(s,""):""}e.exports=function(e,s){if("string"!=typeof e)throw TypeError("First argument must be a string");if(!e)return[];s=s||{};var u=1,d=1;function p(e){var t=e.match(n);t&&(u+=t.length);var r=e.lastIndexOf("\n");d=~r?e.length-r:d+e.length}function h(){var e={line:u,column:d};return function(t){return t.position=new f(e),v(r),t}}function f(e){this.start=e,this.end={line:u,column:d},this.source=s.source}f.prototype.content=e;var m=[];function g(t){var n=Error(s.source+":"+u+":"+d+": "+t);if(n.reason=t,n.filename=s.source,n.line=u,n.column=d,n.source=e,s.silent)m.push(n);else throw n}function v(t){var n=t.exec(e);if(n){var r=n[0];return p(r),e=e.slice(r.length),n}}function y(e){var t;for(e=e||[];t=b();)!1!==t&&e.push(t);return e}function b(){var t=h();if("/"==e.charAt(0)&&"*"==e.charAt(1)){for(var n=2;""!=e.charAt(n)&&("*"!=e.charAt(n)||"/"!=e.charAt(n+1));)++n;if(n+=2,""===e.charAt(n-1))return g("End of comment missing");var r=e.slice(2,n-2);return d+=2,p(r),e=e.slice(n),d+=2,t({type:"comment",comment:r})}}return v(r),function(){var e,n=[];for(y(n);e=function(){var e=h(),n=v(i);if(n){if(b(),!v(o))return g("property missing ':'");var r=v(l),s=e({type:"declaration",property:c(n[0].replace(t,"")),value:r?c(r[0].replace(t,"")):""});return v(a),s}}();)!1!==e&&(n.push(e),y(n));return n}()}},40900:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(80851).Z)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},12070:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(80851).Z)("BookMarked",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20",key:"t4utmx"}],["polyline",{points:"10 2 10 10 13 7 16 10 16 2",key:"13o6vz"}]])},66307:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(80851).Z)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},69669:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(80851).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},31540:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(80851).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},43727:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(80851).Z)("LineChart",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]])},40617:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(80851).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},69436:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(80851).Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},60763:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(80851).Z)("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},57671:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(80851).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},69515:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(80851).Z)("StickyNote",[["path",{d:"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",key:"qazsjp"}],["path",{d:"M15 3v4a2 2 0 0 0 2 2h4",key:"40519r"}]])},98091:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(80851).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},49758:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(80851).Z)("Video",[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]])},99899:(e,t,n)=>{"use strict";var r=n(56715);function i(){}function o(){}o.resetWarningCache=i,e.exports=function(){function e(e,t,n,i,o,l){if(l!==r){var a=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:o,resetWarningCache:i};return n.PropTypes=n,n}},78439:(e,t,n)=>{e.exports=n(99899)()},56715:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},56773:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var n=null;if(!e||"string"!=typeof e)return n;var r=(0,i.default)(e),o="function"==typeof t;return r.forEach(function(e){if("declaration"===e.type){var r=e.property,i=e.value;o?t(r,i,e):i&&((n=n||{})[r]=i)}}),n};var i=r(n(6007))},10180:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>i8});var r={};n.r(r),n.d(r,{boolean:()=>z,booleanish:()=>A,commaOrSpaceSeparated:()=>F,commaSeparated:()=>B,number:()=>R,overloadedBoolean:()=>q,spaceSeparated:()=>O});var i={};n.r(i),n.d(i,{attentionMarkers:()=>tY,contentInitial:()=>tU,disable:()=>tJ,document:()=>tV,flow:()=>t$,flowInitial:()=>tZ,insideSpan:()=>tK,string:()=>tG,text:()=>tW});var o=n(10326),l=n(17577),a=n.n(l),s=n(77506),c=n(40617),u=n(25842),d=n(40588),p=n(92166),h=n(80851);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let f=(0,h.Z)("CheckCheck",[["path",{d:"M18 6 7 17l-5-5",key:"116fxf"}],["path",{d:"m22 10-7.5 7.5L13 16",key:"ke71qq"}]]),m=(0,h.Z)("Reply",[["polyline",{points:"9 17 4 12 9 7",key:"hvgpf2"}],["path",{d:"M20 18v-2a4 4 0 0 0-4-4H4",key:"5vmcpk"}]]);var g=n(94019);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let v=(0,h.Z)("Text",[["path",{d:"M17 6.1H3",key:"wptmhv"}],["path",{d:"M21 12.1H3",key:"1j38uz"}],["path",{d:"M15.1 18H3",key:"1nb16a"}]]),y=(0,h.Z)("Bold",[["path",{d:"M14 12a4 4 0 0 0 0-8H6v8",key:"v2sylx"}],["path",{d:"M15 20a4 4 0 0 0 0-8H6v8Z",key:"1ef5ya"}]]),b=(0,h.Z)("Italic",[["line",{x1:"19",x2:"10",y1:"4",y2:"4",key:"15jd3p"}],["line",{x1:"14",x2:"5",y1:"20",y2:"20",key:"bu0au3"}],["line",{x1:"15",x2:"9",y1:"4",y2:"20",key:"uljnxc"}]]),x=(0,h.Z)("Underline",[["path",{d:"M6 4v6a6 6 0 0 0 12 0V4",key:"9kb039"}],["line",{x1:"4",x2:"20",y1:"20",y2:"20",key:"nun2al"}]]),k=(0,h.Z)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);var w=n(49758),_=n(69436);let C=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,S=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,j={};function E(e,t){return((t||j).jsx?S:C).test(e)}let D=/[ \t\n\f\r]/g;function L(e){return""===e.replace(D,"")}class T{constructor(e,t,n){this.property=e,this.normal=t,n&&(this.space=n)}}function N(e,t){let n={},r={},i=-1;for(;++i<e.length;)Object.assign(n,e[i].property),Object.assign(r,e[i].normal);return new T(n,r,t)}function M(e){return e.toLowerCase()}T.prototype.property={},T.prototype.normal={},T.prototype.space=null;class I{constructor(e,t){this.property=e,this.attribute=t}}I.prototype.space=null,I.prototype.boolean=!1,I.prototype.booleanish=!1,I.prototype.overloadedBoolean=!1,I.prototype.number=!1,I.prototype.commaSeparated=!1,I.prototype.spaceSeparated=!1,I.prototype.commaOrSpaceSeparated=!1,I.prototype.mustUseProperty=!1,I.prototype.defined=!1;let P=0,z=H(),A=H(),q=H(),R=H(),O=H(),B=H(),F=H();function H(){return 2**++P}let V=Object.keys(r);class U extends I{constructor(e,t,n,i){let o=-1;if(super(e,t),function(e,t,n){n&&(e[t]=n)}(this,"space",i),"number"==typeof n)for(;++o<V.length;){let e=V[o];(function(e,t,n){n&&(e[t]=n)})(this,V[o],(n&r[e])===r[e])}}}U.prototype.defined=!0;let Z={}.hasOwnProperty;function $(e){let t;let n={},r={};for(t in e.properties)if(Z.call(e.properties,t)){let i=e.properties[t],o=new U(t,e.transform(e.attributes||{},t),i,e.space);e.mustUseProperty&&e.mustUseProperty.includes(t)&&(o.mustUseProperty=!0),n[t]=o,r[M(t)]=t,r[M(o.attribute)]=t}return new T(n,r,e.space)}let G=$({space:"xlink",transform:(e,t)=>"xlink:"+t.slice(5).toLowerCase(),properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null}}),W=$({space:"xml",transform:(e,t)=>"xml:"+t.slice(3).toLowerCase(),properties:{xmlLang:null,xmlBase:null,xmlSpace:null}});function K(e,t){return t in e?e[t]:t}function Y(e,t){return K(e,t.toLowerCase())}let J=$({space:"xmlns",attributes:{xmlnsxlink:"xmlns:xlink"},transform:Y,properties:{xmlns:null,xmlnsXLink:null}}),Q=$({transform:(e,t)=>"role"===t?t:"aria-"+t.slice(4).toLowerCase(),properties:{ariaActiveDescendant:null,ariaAtomic:A,ariaAutoComplete:null,ariaBusy:A,ariaChecked:A,ariaColCount:R,ariaColIndex:R,ariaColSpan:R,ariaControls:O,ariaCurrent:null,ariaDescribedBy:O,ariaDetails:null,ariaDisabled:A,ariaDropEffect:O,ariaErrorMessage:null,ariaExpanded:A,ariaFlowTo:O,ariaGrabbed:A,ariaHasPopup:null,ariaHidden:A,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:O,ariaLevel:R,ariaLive:null,ariaModal:A,ariaMultiLine:A,ariaMultiSelectable:A,ariaOrientation:null,ariaOwns:O,ariaPlaceholder:null,ariaPosInSet:R,ariaPressed:A,ariaReadOnly:A,ariaRelevant:null,ariaRequired:A,ariaRoleDescription:O,ariaRowCount:R,ariaRowIndex:R,ariaRowSpan:R,ariaSelected:A,ariaSetSize:R,ariaSort:null,ariaValueMax:R,ariaValueMin:R,ariaValueNow:R,ariaValueText:null,role:null}}),X=$({space:"html",attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},transform:Y,mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:B,acceptCharset:O,accessKey:O,action:null,allow:null,allowFullScreen:z,allowPaymentRequest:z,allowUserMedia:z,alt:null,as:null,async:z,autoCapitalize:null,autoComplete:O,autoFocus:z,autoPlay:z,blocking:O,capture:null,charSet:null,checked:z,cite:null,className:O,cols:R,colSpan:null,content:null,contentEditable:A,controls:z,controlsList:O,coords:R|B,crossOrigin:null,data:null,dateTime:null,decoding:null,default:z,defer:z,dir:null,dirName:null,disabled:z,download:q,draggable:A,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:z,formTarget:null,headers:O,height:R,hidden:z,high:R,href:null,hrefLang:null,htmlFor:O,httpEquiv:O,id:null,imageSizes:null,imageSrcSet:null,inert:z,inputMode:null,integrity:null,is:null,isMap:z,itemId:null,itemProp:O,itemRef:O,itemScope:z,itemType:O,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:z,low:R,manifest:null,max:null,maxLength:R,media:null,method:null,min:null,minLength:R,multiple:z,muted:z,name:null,nonce:null,noModule:z,noValidate:z,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:z,optimum:R,pattern:null,ping:O,placeholder:null,playsInline:z,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:z,referrerPolicy:null,rel:O,required:z,reversed:z,rows:R,rowSpan:R,sandbox:O,scope:null,scoped:z,seamless:z,selected:z,shadowRootClonable:z,shadowRootDelegatesFocus:z,shadowRootMode:null,shape:null,size:R,sizes:null,slot:null,span:R,spellCheck:A,src:null,srcDoc:null,srcLang:null,srcSet:null,start:R,step:null,style:null,tabIndex:R,target:null,title:null,translate:null,type:null,typeMustMatch:z,useMap:null,value:A,width:R,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:O,axis:null,background:null,bgColor:null,border:R,borderColor:null,bottomMargin:R,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:z,declare:z,event:null,face:null,frame:null,frameBorder:null,hSpace:R,leftMargin:R,link:null,longDesc:null,lowSrc:null,marginHeight:R,marginWidth:R,noResize:z,noHref:z,noShade:z,noWrap:z,object:null,profile:null,prompt:null,rev:null,rightMargin:R,rules:null,scheme:null,scrolling:A,standby:null,summary:null,text:null,topMargin:R,valueType:null,version:null,vAlign:null,vLink:null,vSpace:R,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:z,disableRemotePlayback:z,prefix:null,property:null,results:R,security:null,unselectable:null}}),ee=$({space:"svg",attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},transform:K,properties:{about:F,accentHeight:R,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:R,amplitude:R,arabicForm:null,ascent:R,attributeName:null,attributeType:null,azimuth:R,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:R,by:null,calcMode:null,capHeight:R,className:O,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:R,diffuseConstant:R,direction:null,display:null,dur:null,divisor:R,dominantBaseline:null,download:z,dx:null,dy:null,edgeMode:null,editable:null,elevation:R,enableBackground:null,end:null,event:null,exponent:R,externalResourcesRequired:null,fill:null,fillOpacity:R,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:B,g2:B,glyphName:B,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:R,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:R,horizOriginX:R,horizOriginY:R,id:null,ideographic:R,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:R,k:R,k1:R,k2:R,k3:R,k4:R,kernelMatrix:F,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:R,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:R,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:R,overlineThickness:R,paintOrder:null,panose1:null,path:null,pathLength:R,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:O,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:R,pointsAtY:R,pointsAtZ:R,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:F,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:F,rev:F,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:F,requiredFeatures:F,requiredFonts:F,requiredFormats:F,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:R,specularExponent:R,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:R,strikethroughThickness:R,string:null,stroke:null,strokeDashArray:F,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:R,strokeOpacity:R,strokeWidth:null,style:null,surfaceScale:R,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:F,tabIndex:R,tableValues:null,target:null,targetX:R,targetY:R,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:F,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:R,underlineThickness:R,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:R,values:null,vAlphabetic:R,vMathematical:R,vectorEffect:null,vHanging:R,vIdeographic:R,version:null,vertAdvY:R,vertOriginX:R,vertOriginY:R,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:R,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null}}),et=N([W,G,J,Q,X],"html"),en=N([W,G,J,Q,ee],"svg"),er=/^data[-\w.:]+$/i,ei=/-[a-z]/g,eo=/[A-Z]/g;function el(e){return"-"+e.toLowerCase()}function ea(e){return e.charAt(1).toUpperCase()}let es={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"};var ec=n(56773);let eu=ec.default||ec,ed=eh("end"),ep=eh("start");function eh(e){return function(t){let n=t&&t.position&&t.position[e]||{};if("number"==typeof n.line&&n.line>0&&"number"==typeof n.column&&n.column>0)return{line:n.line,column:n.column,offset:"number"==typeof n.offset&&n.offset>-1?n.offset:void 0}}}function ef(e){return e&&"object"==typeof e?"position"in e||"type"in e?eg(e.position):"start"in e||"end"in e?eg(e):"line"in e||"column"in e?em(e):"":""}function em(e){return ev(e&&e.line)+":"+ev(e&&e.column)}function eg(e){return em(e&&e.start)+"-"+em(e&&e.end)}function ev(e){return e&&"number"==typeof e?e:1}class ey extends Error{constructor(e,t,n){super(),"string"==typeof t&&(n=t,t=void 0);let r="",i={},o=!1;if(t&&(i="line"in t&&"column"in t?{place:t}:"start"in t&&"end"in t?{place:t}:"type"in t?{ancestors:[t],place:t.position}:{...t}),"string"==typeof e?r=e:!i.cause&&e&&(o=!0,r=e.message,i.cause=e),!i.ruleId&&!i.source&&"string"==typeof n){let e=n.indexOf(":");-1===e?i.ruleId=n:(i.source=n.slice(0,e),i.ruleId=n.slice(e+1))}if(!i.place&&i.ancestors&&i.ancestors){let e=i.ancestors[i.ancestors.length-1];e&&(i.place=e.position)}let l=i.place&&"start"in i.place?i.place.start:i.place;this.ancestors=i.ancestors||void 0,this.cause=i.cause||void 0,this.column=l?l.column:void 0,this.fatal=void 0,this.file,this.message=r,this.line=l?l.line:void 0,this.name=ef(i.place)||"1:1",this.place=i.place||void 0,this.reason=this.message,this.ruleId=i.ruleId||void 0,this.source=i.source||void 0,this.stack=o&&i.cause&&"string"==typeof i.cause.stack?i.cause.stack:"",this.actual,this.expected,this.note,this.url}}ey.prototype.file="",ey.prototype.name="",ey.prototype.reason="",ey.prototype.message="",ey.prototype.stack="",ey.prototype.column=void 0,ey.prototype.line=void 0,ey.prototype.ancestors=void 0,ey.prototype.cause=void 0,ey.prototype.fatal=void 0,ey.prototype.place=void 0,ey.prototype.ruleId=void 0,ey.prototype.source=void 0;let eb={}.hasOwnProperty,ex=new Map,ek=/[A-Z]/g,ew=/-([a-z])/g,e_=new Set(["table","tbody","thead","tfoot","tr"]),eC=new Set(["td","th"]),eS="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function ej(e,t,n){return"element"===t.type?function(e,t,n){let r=e.schema,i=r;"svg"===t.tagName.toLowerCase()&&"html"===r.space&&(i=en,e.schema=i),e.ancestors.push(t);let o=eT(e,t.tagName,!1),l=function(e,t){let n,r;let i={};for(r in t.properties)if("children"!==r&&eb.call(t.properties,r)){let o=function(e,t,n){let r=function(e,t){let n=M(t),r=t,i=I;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&"data"===n.slice(0,4)&&er.test(t)){if("-"===t.charAt(4)){let e=t.slice(5).replace(ei,ea);r="data"+e.charAt(0).toUpperCase()+e.slice(1)}else{let e=t.slice(4);if(!ei.test(e)){let n=e.replace(eo,el);"-"!==n.charAt(0)&&(n="-"+n),t="data"+n}}i=U}return new i(r,t)}(e.schema,t);if(!(null==n||"number"==typeof n&&Number.isNaN(n))){if(Array.isArray(n)&&(n=r.commaSeparated?function(e,t){let n={};return(""===e[e.length-1]?[...e,""]:e).join((n.padRight?" ":"")+","+(!1===n.padLeft?"":" ")).trim()}(n):n.join(" ").trim()),"style"===r.property){let t="object"==typeof n?n:function(e,t){let n={};try{eu(t,function(e,t){let r=e;"--"!==r.slice(0,2)&&("-ms-"===r.slice(0,4)&&(r="ms-"+r.slice(4)),r=r.replace(ew,eM)),n[r]=t})}catch(t){if(!e.ignoreInvalidStyle){let n=new ey("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:t,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=eS+"#cannot-parse-style-attribute",n}}return n}(e,String(n));return"css"===e.stylePropertyNameCase&&(t=function(e){let t;let n={};for(t in e)eb.call(e,t)&&(n[function(e){let t=e.replace(ek,eI);return"ms-"===t.slice(0,3)&&(t="-"+t),t}(t)]=e[t]);return n}(t)),["style",t]}return["react"===e.elementAttributeNameCase&&r.space?es[r.property]||r.property:r.attribute,n]}}(e,r,t.properties[r]);if(o){let[r,l]=o;e.tableCellAlignToStyle&&"align"===r&&"string"==typeof l&&eC.has(t.tagName)?n=l:i[r]=l}}return n&&((i.style||(i.style={}))["css"===e.stylePropertyNameCase?"text-align":"textAlign"]=n),i}(e,t),a=eL(e,t);return e_.has(t.tagName)&&(a=a.filter(function(e){return"string"!=typeof e||!("object"==typeof e?"text"===e.type&&L(e.value):L(e))})),eE(e,l,o,t),eD(l,a),e.ancestors.pop(),e.schema=r,e.create(t,o,l,n)}(e,t,n):"mdxFlowExpression"===t.type||"mdxTextExpression"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater){let n=t.data.estree.body[0];return n.type,e.evaluater.evaluateExpression(n.expression)}eN(e,t.position)}(e,t):"mdxJsxFlowElement"===t.type||"mdxJsxTextElement"===t.type?function(e,t,n){let r=e.schema,i=r;"svg"===t.name&&"html"===r.space&&(i=en,e.schema=i),e.ancestors.push(t);let o=null===t.name?e.Fragment:eT(e,t.name,!0),l=function(e,t){let n={};for(let r of t.attributes)if("mdxJsxExpressionAttribute"===r.type){if(r.data&&r.data.estree&&e.evaluater){let t=r.data.estree.body[0];t.type;let i=t.expression;i.type;let o=i.properties[0];o.type,Object.assign(n,e.evaluater.evaluateExpression(o.argument))}else eN(e,t.position)}else{let i;let o=r.name;if(r.value&&"object"==typeof r.value){if(r.value.data&&r.value.data.estree&&e.evaluater){let t=r.value.data.estree.body[0];t.type,i=e.evaluater.evaluateExpression(t.expression)}else eN(e,t.position)}else i=null===r.value||r.value;n[o]=i}return n}(e,t),a=eL(e,t);return eE(e,l,o,t),eD(l,a),e.ancestors.pop(),e.schema=r,e.create(t,o,l,n)}(e,t,n):"mdxjsEsm"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(t.data.estree);eN(e,t.position)}(e,t):"root"===t.type?function(e,t,n){let r={};return eD(r,eL(e,t)),e.create(t,e.Fragment,r,n)}(e,t,n):"text"===t.type?t.value:void 0}function eE(e,t,n,r){"string"!=typeof n&&n!==e.Fragment&&e.passNode&&(t.node=r)}function eD(e,t){if(t.length>0){let n=t.length>1?t:t[0];n&&(e.children=n)}}function eL(e,t){let n=[],r=-1,i=e.passKeys?new Map:ex;for(;++r<t.children.length;){let o;let l=t.children[r];if(e.passKeys){let e="element"===l.type?l.tagName:"mdxJsxFlowElement"===l.type||"mdxJsxTextElement"===l.type?l.name:void 0;if(e){let t=i.get(e)||0;o=e+"-"+t,i.set(e,t+1)}}let a=ej(e,l,o);void 0!==a&&n.push(a)}return n}function eT(e,t,n){let r;if(n){if(t.includes(".")){let e;let n=t.split("."),i=-1;for(;++i<n.length;){let t=E(n[i])?{type:"Identifier",name:n[i]}:{type:"Literal",value:n[i]};e=e?{type:"MemberExpression",object:e,property:t,computed:!!(i&&"Literal"===t.type),optional:!1}:t}r=e}else r=E(t)&&!/^[a-z]/.test(t)?{type:"Identifier",name:t}:{type:"Literal",value:t}}else r={type:"Literal",value:t};if("Literal"===r.type){let t=r.value;return eb.call(e.components,t)?e.components[t]:t}if(e.evaluater)return e.evaluater.evaluateExpression(r);eN(e)}function eN(e,t){let n=new ey("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:t,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=eS+"#cannot-handle-mdx-estrees-without-createevaluater",n}function eM(e,t){return t.toUpperCase()}function eI(e){return"-"+e.toLowerCase()}let eP={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]},ez={};function eA(e,t,n){if(e&&"object"==typeof e){if("value"in e)return"html"!==e.type||n?e.value:"";if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return eq(e.children,t,n)}return Array.isArray(e)?eq(e,t,n):""}function eq(e,t,n){let r=[],i=-1;for(;++i<e.length;)r[i]=eA(e[i],t,n);return r.join("")}function eR(e,t,n,r){let i;let o=e.length,l=0;if(t=t<0?-t>o?0:o+t:t>o?o:t,n=n>0?n:0,r.length<1e4)(i=Array.from(r)).unshift(t,n),e.splice(...i);else for(n&&e.splice(t,n);l<r.length;)(i=r.slice(l,l+1e4)).unshift(t,0),e.splice(...i),l+=1e4,t+=1e4}function eO(e,t){return e.length>0?(eR(e,e.length,0,t),e):t}class eB{constructor(e){this.left=e?[...e]:[],this.right=[]}get(e){if(e<0||e>=this.left.length+this.right.length)throw RangeError("Cannot access index `"+e+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return e<this.left.length?this.left[e]:this.right[this.right.length-e+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(e,t){let n=null==t?Number.POSITIVE_INFINITY:t;return n<this.left.length?this.left.slice(e,n):e>this.left.length?this.right.slice(this.right.length-n+this.left.length,this.right.length-e+this.left.length).reverse():this.left.slice(e).concat(this.right.slice(this.right.length-n+this.left.length).reverse())}splice(e,t,n){this.setCursor(Math.trunc(e));let r=this.right.splice(this.right.length-(t||0),Number.POSITIVE_INFINITY);return n&&eF(this.left,n),r.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(e){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(e)}pushMany(e){this.setCursor(Number.POSITIVE_INFINITY),eF(this.left,e)}unshift(e){this.setCursor(0),this.right.push(e)}unshiftMany(e){this.setCursor(0),eF(this.right,e.reverse())}setCursor(e){if(e!==this.left.length&&(!(e>this.left.length)||0!==this.right.length)&&(!(e<0)||0!==this.left.length)){if(e<this.left.length){let t=this.left.splice(e,Number.POSITIVE_INFINITY);eF(this.right,t.reverse())}else{let t=this.right.splice(this.left.length+this.right.length-e,Number.POSITIVE_INFINITY);eF(this.left,t.reverse())}}}}function eF(e,t){let n=0;if(t.length<1e4)e.push(...t);else for(;n<t.length;)e.push(...t.slice(n,n+1e4)),n+=1e4}function eH(e){let t,n,r,i,o,l,a;let s={},c=-1,u=new eB(e);for(;++c<u.length;){for(;(c in s);)c=s[c];if(t=u.get(c),c&&"chunkFlow"===t[1].type&&"listItemPrefix"===u.get(c-1)[1].type&&((r=0)<(l=t[1]._tokenizer.events).length&&"lineEndingBlank"===l[r][1].type&&(r+=2),r<l.length&&"content"===l[r][1].type))for(;++r<l.length&&"content"!==l[r][1].type;)"chunkText"===l[r][1].type&&(l[r][1]._isInFirstContentOfListItem=!0,r++);if("enter"===t[0])t[1].contentType&&(Object.assign(s,function(e,t){let n,r;let i=e.get(t)[1],o=e.get(t)[2],l=t-1,a=[],s=i._tokenizer||o.parser[i.contentType](i.start),c=s.events,u=[],d={},p=-1,h=i,f=0,m=0,g=[0];for(;h;){for(;e.get(++l)[1]!==h;);a.push(l),!h._tokenizer&&(n=o.sliceStream(h),h.next||n.push(null),r&&s.defineSkip(h.start),h._isInFirstContentOfListItem&&(s._gfmTasklistFirstContentOfListItem=!0),s.write(n),h._isInFirstContentOfListItem&&(s._gfmTasklistFirstContentOfListItem=void 0)),r=h,h=h.next}for(h=i;++p<c.length;)"exit"===c[p][0]&&"enter"===c[p-1][0]&&c[p][1].type===c[p-1][1].type&&c[p][1].start.line!==c[p][1].end.line&&(m=p+1,g.push(m),h._tokenizer=void 0,h.previous=void 0,h=h.next);for(s.events=[],h?(h._tokenizer=void 0,h.previous=void 0):g.pop(),p=g.length;p--;){let t=c.slice(g[p],g[p+1]),n=a.pop();u.push([n,n+t.length-1]),e.splice(n,2,t)}for(u.reverse(),p=-1;++p<u.length;)d[f+u[p][0]]=f+u[p][1],f+=u[p][1]-u[p][0]-1;return d}(u,c)),c=s[c],a=!0);else if(t[1]._container){for(r=c,n=void 0;r--;)if("lineEnding"===(i=u.get(r))[1].type||"lineEndingBlank"===i[1].type)"enter"===i[0]&&(n&&(u.get(n)[1].type="lineEndingBlank"),i[1].type="lineEnding",n=r);else break;n&&(t[1].end={...u.get(n)[1].start},(o=u.slice(n,c)).unshift(t),u.splice(n,c-n+1,o))}}return eR(e,0,Number.POSITIVE_INFINITY,u.slice(0)),!a}let eV={}.hasOwnProperty,eU=e2(/[A-Za-z]/),eZ=e2(/[\dA-Za-z]/),e$=e2(/[#-'*+\--9=?A-Z^-~]/);function eG(e){return null!==e&&(e<32||127===e)}let eW=e2(/\d/),eK=e2(/[\dA-Fa-f]/),eY=e2(/[!-/:-@[-`{-~]/);function eJ(e){return null!==e&&e<-2}function eQ(e){return null!==e&&(e<0||32===e)}function eX(e){return -2===e||-1===e||32===e}let e1=e2(/\p{P}|\p{S}/u),e0=e2(/\s/);function e2(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}function e3(e,t,n,r){let i=r?r-1:Number.POSITIVE_INFINITY,o=0;return function(r){return eX(r)?(e.enter(n),function r(l){return eX(l)&&o++<i?(e.consume(l),r):(e.exit(n),t(l))}(r)):t(r)}}let e4={tokenize:function(e){let t;let n=e.attempt(this.parser.constructs.contentInitial,function(t){if(null===t){e.consume(t);return}return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),e3(e,n,"linePrefix")},function(n){return e.enter("paragraph"),function n(r){let i=e.enter("chunkText",{contentType:"text",previous:t});return t&&(t.next=i),t=i,function t(r){if(null===r){e.exit("chunkText"),e.exit("paragraph"),e.consume(r);return}return eJ(r)?(e.consume(r),e.exit("chunkText"),n):(e.consume(r),t)}(r)}(n)});return n}},e5={tokenize:function(e){let t,n,r;let i=this,o=[],l=0;return a;function a(t){if(l<o.length){let n=o[l];return i.containerState=n[1],e.attempt(n[0].continuation,s,c)(t)}return c(t)}function s(e){if(l++,i.containerState._closeFlow){let n;i.containerState._closeFlow=void 0,t&&v();let r=i.events.length,o=r;for(;o--;)if("exit"===i.events[o][0]&&"chunkFlow"===i.events[o][1].type){n=i.events[o][1].end;break}g(l);let a=r;for(;a<i.events.length;)i.events[a][1].end={...n},a++;return eR(i.events,o+1,0,i.events.slice(r)),i.events.length=a,c(e)}return a(e)}function c(n){if(l===o.length){if(!t)return p(n);if(t.currentConstruct&&t.currentConstruct.concrete)return f(n);i.interrupt=!!(t.currentConstruct&&!t._gfmTableDynamicInterruptHack)}return i.containerState={},e.check(e6,u,d)(n)}function u(e){return t&&v(),g(l),p(e)}function d(e){return i.parser.lazy[i.now().line]=l!==o.length,r=i.now().offset,f(e)}function p(t){return i.containerState={},e.attempt(e6,h,f)(t)}function h(e){return l++,o.push([i.currentConstruct,i.containerState]),p(e)}function f(r){if(null===r){t&&v(),g(0),e.consume(r);return}return t=t||i.parser.flow(i.now()),e.enter("chunkFlow",{_tokenizer:t,contentType:"flow",previous:n}),function t(n){if(null===n){m(e.exit("chunkFlow"),!0),g(0),e.consume(n);return}return eJ(n)?(e.consume(n),m(e.exit("chunkFlow")),l=0,i.interrupt=void 0,a):(e.consume(n),t)}(r)}function m(e,o){let a=i.sliceStream(e);if(o&&a.push(null),e.previous=n,n&&(n.next=e),n=e,t.defineSkip(e.start),t.write(a),i.parser.lazy[e.start.line]){let e,n,o=t.events.length;for(;o--;)if(t.events[o][1].start.offset<r&&(!t.events[o][1].end||t.events[o][1].end.offset>r))return;let a=i.events.length,s=a;for(;s--;)if("exit"===i.events[s][0]&&"chunkFlow"===i.events[s][1].type){if(e){n=i.events[s][1].end;break}e=!0}for(g(l),o=a;o<i.events.length;)i.events[o][1].end={...n},o++;eR(i.events,s+1,0,i.events.slice(a)),i.events.length=o}}function g(t){let n=o.length;for(;n-- >t;){let t=o[n];i.containerState=t[1],t[0].exit.call(i,e)}o.length=t}function v(){t.write([null]),n=void 0,t=void 0,i.containerState._closeFlow=void 0}}},e6={tokenize:function(e,t,n){return e3(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}},e7={partial:!0,tokenize:function(e,t,n){return function(t){return eX(t)?e3(e,r,"linePrefix")(t):r(t)};function r(e){return null===e||eJ(e)?t(e):n(e)}}},e9={resolve:function(e){return eH(e),e},tokenize:function(e,t){let n;return function(t){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),r(t)};function r(t){return null===t?i(t):eJ(t)?e.check(e8,o,i)(t):(e.consume(t),r)}function i(n){return e.exit("chunkContent"),e.exit("content"),t(n)}function o(t){return e.consume(t),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,r}}},e8={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),e3(e,i,"linePrefix")};function i(i){if(null===i||eJ(i))return n(i);let o=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&o&&"linePrefix"===o[1].type&&o[2].sliceSerialize(o[1],!0).length>=4?t(i):e.interrupt(r.parser.constructs.flow,n,t)(i)}}},te={tokenize:function(e){let t=this,n=e.attempt(e7,function(r){if(null===r){e.consume(r);return}return e.enter("lineEndingBlank"),e.consume(r),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n},e.attempt(this.parser.constructs.flowInitial,r,e3(e,e.attempt(this.parser.constructs.flow,r,e.attempt(e9,r)),"linePrefix")));return n;function r(r){if(null===r){e.consume(r);return}return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),t.currentConstruct=void 0,n}}},tt={resolveAll:to()},tn=ti("string"),tr=ti("text");function ti(e){return{resolveAll:to("text"===e?tl:void 0),tokenize:function(t){let n=this,r=this.parser.constructs[e],i=t.attempt(r,o,l);return o;function o(e){return s(e)?i(e):l(e)}function l(e){if(null===e){t.consume(e);return}return t.enter("data"),t.consume(e),a}function a(e){return s(e)?(t.exit("data"),i(e)):(t.consume(e),a)}function s(e){if(null===e)return!0;let t=r[e],i=-1;if(t)for(;++i<t.length;){let e=t[i];if(!e.previous||e.previous.call(n,n.previous))return!0}return!1}}}}function to(e){return function(t,n){let r,i=-1;for(;++i<=t.length;)void 0===r?t[i]&&"data"===t[i][1].type&&(r=i,i++):t[i]&&"data"===t[i][1].type||(i!==r+2&&(t[r][1].end=t[i-1][1].end,t.splice(r+2,i-r-2),i=r+2),r=void 0);return e?e(t,n):t}}function tl(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||"lineEnding"===e[n][1].type)&&"data"===e[n-1][1].type){let r;let i=e[n-1][1],o=t.sliceStream(i),l=o.length,a=-1,s=0;for(;l--;){let e=o[l];if("string"==typeof e){for(a=e.length;32===e.charCodeAt(a-1);)s++,a--;if(a)break;a=-1}else if(-2===e)r=!0,s++;else if(-1===e);else{l++;break}}if(s){let o={type:n===e.length||r||s<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:l?a:i.start._bufferIndex+a,_index:i.start._index+l,line:i.end.line,column:i.end.column-s,offset:i.end.offset-s},end:{...i.end}};i.end={...o.start},i.start.offset===i.end.offset?Object.assign(i,o):(e.splice(n,0,["enter",o,t],["exit",o,t]),n+=2)}n++}return e}let ta={name:"thematicBreak",tokenize:function(e,t,n){let r,i=0;return function(o){return e.enter("thematicBreak"),r=o,function o(l){return l===r?(e.enter("thematicBreakSequence"),function t(n){return n===r?(e.consume(n),i++,t):(e.exit("thematicBreakSequence"),eX(n)?e3(e,o,"whitespace")(n):o(n))}(l)):i>=3&&(null===l||eJ(l))?(e.exit("thematicBreak"),t(l)):n(l)}(o)}}},ts={continuation:{tokenize:function(e,t,n){let r=this;return r.containerState._closeFlow=void 0,e.check(e7,function(n){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,e3(e,t,"listItemIndent",r.containerState.size+1)(n)},function(n){return r.containerState.furtherBlankLines||!eX(n)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,i(n)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(tu,t,i)(n))});function i(i){return r.containerState._closeFlow=!0,r.interrupt=void 0,e3(e,e.attempt(ts,t,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(i)}}},exit:function(e){e.exit(this.containerState.type)},name:"list",tokenize:function(e,t,n){let r=this,i=r.events[r.events.length-1],o=i&&"linePrefix"===i[1].type?i[2].sliceSerialize(i[1],!0).length:0,l=0;return function(t){let i=r.containerState.type||(42===t||43===t||45===t?"listUnordered":"listOrdered");if("listUnordered"===i?!r.containerState.marker||t===r.containerState.marker:eW(t)){if(r.containerState.type||(r.containerState.type=i,e.enter(i,{_container:!0})),"listUnordered"===i)return e.enter("listItemPrefix"),42===t||45===t?e.check(ta,n,a)(t):a(t);if(!r.interrupt||49===t)return e.enter("listItemPrefix"),e.enter("listItemValue"),function t(i){return eW(i)&&++l<10?(e.consume(i),t):(!r.interrupt||l<2)&&(r.containerState.marker?i===r.containerState.marker:41===i||46===i)?(e.exit("listItemValue"),a(i)):n(i)}(t)}return n(t)};function a(t){return e.enter("listItemMarker"),e.consume(t),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||t,e.check(e7,r.interrupt?n:s,e.attempt(tc,u,c))}function s(e){return r.containerState.initialBlankLine=!0,o++,u(e)}function c(t){return eX(t)?(e.enter("listItemPrefixWhitespace"),e.consume(t),e.exit("listItemPrefixWhitespace"),u):n(t)}function u(n){return r.containerState.size=o+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(n)}}},tc={partial:!0,tokenize:function(e,t,n){let r=this;return e3(e,function(e){let i=r.events[r.events.length-1];return!eX(e)&&i&&"listItemPrefixWhitespace"===i[1].type?t(e):n(e)},"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5)}},tu={partial:!0,tokenize:function(e,t,n){let r=this;return e3(e,function(e){let i=r.events[r.events.length-1];return i&&"listItemIndent"===i[1].type&&i[2].sliceSerialize(i[1],!0).length===r.containerState.size?t(e):n(e)},"listItemIndent",r.containerState.size+1)}},td={continuation:{tokenize:function(e,t,n){let r=this;return function(t){return eX(t)?e3(e,i,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):i(t)};function i(r){return e.attempt(td,t,n)(r)}}},exit:function(e){e.exit("blockQuote")},name:"blockQuote",tokenize:function(e,t,n){let r=this;return function(t){if(62===t){let n=r.containerState;return n.open||(e.enter("blockQuote",{_container:!0}),n.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(t),e.exit("blockQuoteMarker"),i}return n(t)};function i(n){return eX(n)?(e.enter("blockQuotePrefixWhitespace"),e.consume(n),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(n))}}};function tp(e,t,n,r,i,o,l,a,s){let c=s||Number.POSITIVE_INFINITY,u=0;return function(t){return 60===t?(e.enter(r),e.enter(i),e.enter(o),e.consume(t),e.exit(o),d):null===t||32===t||41===t||eG(t)?n(t):(e.enter(r),e.enter(l),e.enter(a),e.enter("chunkString",{contentType:"string"}),f(t))};function d(n){return 62===n?(e.enter(o),e.consume(n),e.exit(o),e.exit(i),e.exit(r),t):(e.enter(a),e.enter("chunkString",{contentType:"string"}),p(n))}function p(t){return 62===t?(e.exit("chunkString"),e.exit(a),d(t)):null===t||60===t||eJ(t)?n(t):(e.consume(t),92===t?h:p)}function h(t){return 60===t||62===t||92===t?(e.consume(t),p):p(t)}function f(i){return!u&&(null===i||41===i||eQ(i))?(e.exit("chunkString"),e.exit(a),e.exit(l),e.exit(r),t(i)):u<c&&40===i?(e.consume(i),u++,f):41===i?(e.consume(i),u--,f):null===i||32===i||40===i||eG(i)?n(i):(e.consume(i),92===i?m:f)}function m(t){return 40===t||41===t||92===t?(e.consume(t),f):f(t)}}function th(e,t,n,r,i,o){let l;let a=this,s=0;return function(t){return e.enter(r),e.enter(i),e.consume(t),e.exit(i),e.enter(o),c};function c(d){return s>999||null===d||91===d||93===d&&!l||94===d&&!s&&"_hiddenFootnoteSupport"in a.parser.constructs?n(d):93===d?(e.exit(o),e.enter(i),e.consume(d),e.exit(i),e.exit(r),t):eJ(d)?(e.enter("lineEnding"),e.consume(d),e.exit("lineEnding"),c):(e.enter("chunkString",{contentType:"string"}),u(d))}function u(t){return null===t||91===t||93===t||eJ(t)||s++>999?(e.exit("chunkString"),c(t)):(e.consume(t),l||(l=!eX(t)),92===t?d:u)}function d(t){return 91===t||92===t||93===t?(e.consume(t),s++,u):u(t)}}function tf(e,t,n,r,i,o){let l;return function(t){return 34===t||39===t||40===t?(e.enter(r),e.enter(i),e.consume(t),e.exit(i),l=40===t?41:t,a):n(t)};function a(n){return n===l?(e.enter(i),e.consume(n),e.exit(i),e.exit(r),t):(e.enter(o),s(n))}function s(t){return t===l?(e.exit(o),a(l)):null===t?n(t):eJ(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),e3(e,s,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),c(t))}function c(t){return t===l||null===t||eJ(t)?(e.exit("chunkString"),s(t)):(e.consume(t),92===t?u:c)}function u(t){return t===l||92===t?(e.consume(t),c):c(t)}}function tm(e,t){let n;return function r(i){return eJ(i)?(e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),n=!0,r):eX(i)?e3(e,r,n?"linePrefix":"lineSuffix")(i):t(i)}}function tg(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}let tv={partial:!0,tokenize:function(e,t,n){return function(t){return eQ(t)?tm(e,r)(t):n(t)};function r(t){return tf(e,i,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(t)}function i(t){return eX(t)?e3(e,o,"whitespace")(t):o(t)}function o(e){return null===e||eJ(e)?t(e):n(e)}}},ty={name:"codeIndented",tokenize:function(e,t,n){let r=this;return function(t){return e.enter("codeIndented"),e3(e,i,"linePrefix",5)(t)};function i(t){let i=r.events[r.events.length-1];return i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?function t(n){return null===n?o(n):eJ(n)?e.attempt(tb,t,o)(n):(e.enter("codeFlowValue"),function n(r){return null===r||eJ(r)?(e.exit("codeFlowValue"),t(r)):(e.consume(r),n)}(n))}(t):n(t)}function o(n){return e.exit("codeIndented"),t(n)}}},tb={partial:!0,tokenize:function(e,t,n){let r=this;return i;function i(t){return r.parser.lazy[r.now().line]?n(t):eJ(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i):e3(e,o,"linePrefix",5)(t)}function o(e){let o=r.events[r.events.length-1];return o&&"linePrefix"===o[1].type&&o[2].sliceSerialize(o[1],!0).length>=4?t(e):eJ(e)?i(e):n(e)}}},tx={name:"setextUnderline",resolveTo:function(e,t){let n,r,i,o=e.length;for(;o--;)if("enter"===e[o][0]){if("content"===e[o][1].type){n=o;break}"paragraph"===e[o][1].type&&(r=o)}else"content"===e[o][1].type&&e.splice(o,1),i||"definition"!==e[o][1].type||(i=o);let l={type:"setextHeading",start:{...e[r][1].start},end:{...e[e.length-1][1].end}};return e[r][1].type="setextHeadingText",i?(e.splice(r,0,["enter",l,t]),e.splice(i+1,0,["exit",e[n][1],t]),e[n][1].end={...e[i][1].end}):e[n][1]=l,e.push(["exit",l,t]),e},tokenize:function(e,t,n){let r;let i=this;return function(t){let l,a=i.events.length;for(;a--;)if("lineEnding"!==i.events[a][1].type&&"linePrefix"!==i.events[a][1].type&&"content"!==i.events[a][1].type){l="paragraph"===i.events[a][1].type;break}return!i.parser.lazy[i.now().line]&&(i.interrupt||l)?(e.enter("setextHeadingLine"),r=t,e.enter("setextHeadingLineSequence"),function t(n){return n===r?(e.consume(n),t):(e.exit("setextHeadingLineSequence"),eX(n)?e3(e,o,"lineSuffix")(n):o(n))}(t)):n(t)};function o(r){return null===r||eJ(r)?(e.exit("setextHeadingLine"),t(r)):n(r)}}},tk=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],tw=["pre","script","style","textarea"],t_={partial:!0,tokenize:function(e,t,n){return function(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),e.attempt(e7,t,n)}}},tC={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return eJ(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i):n(t)};function i(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},tS={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return null===t?n(t):(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i)};function i(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},tj={concrete:!0,name:"codeFenced",tokenize:function(e,t,n){let r;let i=this,o={partial:!0,tokenize:function(e,t,n){let o=0;return function(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),l};function l(t){return e.enter("codeFencedFence"),eX(t)?e3(e,s,"linePrefix",i.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):s(t)}function s(t){return t===r?(e.enter("codeFencedFenceSequence"),function t(i){return i===r?(o++,e.consume(i),t):o>=a?(e.exit("codeFencedFenceSequence"),eX(i)?e3(e,c,"whitespace")(i):c(i)):n(i)}(t)):n(t)}function c(r){return null===r||eJ(r)?(e.exit("codeFencedFence"),t(r)):n(r)}}},l=0,a=0;return function(t){return function(t){let o=i.events[i.events.length-1];return l=o&&"linePrefix"===o[1].type?o[2].sliceSerialize(o[1],!0).length:0,r=t,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),function t(i){return i===r?(a++,e.consume(i),t):a<3?n(i):(e.exit("codeFencedFenceSequence"),eX(i)?e3(e,s,"whitespace")(i):s(i))}(t)}(t)};function s(o){return null===o||eJ(o)?(e.exit("codeFencedFence"),i.interrupt?t(o):e.check(tS,u,f)(o)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),function t(i){return null===i||eJ(i)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),s(i)):eX(i)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),e3(e,c,"whitespace")(i)):96===i&&i===r?n(i):(e.consume(i),t)}(o))}function c(t){return null===t||eJ(t)?s(t):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),function t(i){return null===i||eJ(i)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),s(i)):96===i&&i===r?n(i):(e.consume(i),t)}(t))}function u(t){return e.attempt(o,f,d)(t)}function d(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),p}function p(t){return l>0&&eX(t)?e3(e,h,"linePrefix",l+1)(t):h(t)}function h(t){return null===t||eJ(t)?e.check(tS,u,f)(t):(e.enter("codeFlowValue"),function t(n){return null===n||eJ(n)?(e.exit("codeFlowValue"),h(n)):(e.consume(n),t)}(t))}function f(n){return e.exit("codeFenced"),t(n)}}},tE={AElig:"\xc6",AMP:"&",Aacute:"\xc1",Abreve:"Ă",Acirc:"\xc2",Acy:"А",Afr:"\uD835\uDD04",Agrave:"\xc0",Alpha:"Α",Amacr:"Ā",And:"⩓",Aogon:"Ą",Aopf:"\uD835\uDD38",ApplyFunction:"⁡",Aring:"\xc5",Ascr:"\uD835\uDC9C",Assign:"≔",Atilde:"\xc3",Auml:"\xc4",Backslash:"∖",Barv:"⫧",Barwed:"⌆",Bcy:"Б",Because:"∵",Bernoullis:"ℬ",Beta:"Β",Bfr:"\uD835\uDD05",Bopf:"\uD835\uDD39",Breve:"˘",Bscr:"ℬ",Bumpeq:"≎",CHcy:"Ч",COPY:"\xa9",Cacute:"Ć",Cap:"⋒",CapitalDifferentialD:"ⅅ",Cayleys:"ℭ",Ccaron:"Č",Ccedil:"\xc7",Ccirc:"Ĉ",Cconint:"∰",Cdot:"Ċ",Cedilla:"\xb8",CenterDot:"\xb7",Cfr:"ℭ",Chi:"Χ",CircleDot:"⊙",CircleMinus:"⊖",CirclePlus:"⊕",CircleTimes:"⊗",ClockwiseContourIntegral:"∲",CloseCurlyDoubleQuote:"”",CloseCurlyQuote:"’",Colon:"∷",Colone:"⩴",Congruent:"≡",Conint:"∯",ContourIntegral:"∮",Copf:"ℂ",Coproduct:"∐",CounterClockwiseContourIntegral:"∳",Cross:"⨯",Cscr:"\uD835\uDC9E",Cup:"⋓",CupCap:"≍",DD:"ⅅ",DDotrahd:"⤑",DJcy:"Ђ",DScy:"Ѕ",DZcy:"Џ",Dagger:"‡",Darr:"↡",Dashv:"⫤",Dcaron:"Ď",Dcy:"Д",Del:"∇",Delta:"Δ",Dfr:"\uD835\uDD07",DiacriticalAcute:"\xb4",DiacriticalDot:"˙",DiacriticalDoubleAcute:"˝",DiacriticalGrave:"`",DiacriticalTilde:"˜",Diamond:"⋄",DifferentialD:"ⅆ",Dopf:"\uD835\uDD3B",Dot:"\xa8",DotDot:"⃜",DotEqual:"≐",DoubleContourIntegral:"∯",DoubleDot:"\xa8",DoubleDownArrow:"⇓",DoubleLeftArrow:"⇐",DoubleLeftRightArrow:"⇔",DoubleLeftTee:"⫤",DoubleLongLeftArrow:"⟸",DoubleLongLeftRightArrow:"⟺",DoubleLongRightArrow:"⟹",DoubleRightArrow:"⇒",DoubleRightTee:"⊨",DoubleUpArrow:"⇑",DoubleUpDownArrow:"⇕",DoubleVerticalBar:"∥",DownArrow:"↓",DownArrowBar:"⤓",DownArrowUpArrow:"⇵",DownBreve:"̑",DownLeftRightVector:"⥐",DownLeftTeeVector:"⥞",DownLeftVector:"↽",DownLeftVectorBar:"⥖",DownRightTeeVector:"⥟",DownRightVector:"⇁",DownRightVectorBar:"⥗",DownTee:"⊤",DownTeeArrow:"↧",Downarrow:"⇓",Dscr:"\uD835\uDC9F",Dstrok:"Đ",ENG:"Ŋ",ETH:"\xd0",Eacute:"\xc9",Ecaron:"Ě",Ecirc:"\xca",Ecy:"Э",Edot:"Ė",Efr:"\uD835\uDD08",Egrave:"\xc8",Element:"∈",Emacr:"Ē",EmptySmallSquare:"◻",EmptyVerySmallSquare:"▫",Eogon:"Ę",Eopf:"\uD835\uDD3C",Epsilon:"Ε",Equal:"⩵",EqualTilde:"≂",Equilibrium:"⇌",Escr:"ℰ",Esim:"⩳",Eta:"Η",Euml:"\xcb",Exists:"∃",ExponentialE:"ⅇ",Fcy:"Ф",Ffr:"\uD835\uDD09",FilledSmallSquare:"◼",FilledVerySmallSquare:"▪",Fopf:"\uD835\uDD3D",ForAll:"∀",Fouriertrf:"ℱ",Fscr:"ℱ",GJcy:"Ѓ",GT:">",Gamma:"Γ",Gammad:"Ϝ",Gbreve:"Ğ",Gcedil:"Ģ",Gcirc:"Ĝ",Gcy:"Г",Gdot:"Ġ",Gfr:"\uD835\uDD0A",Gg:"⋙",Gopf:"\uD835\uDD3E",GreaterEqual:"≥",GreaterEqualLess:"⋛",GreaterFullEqual:"≧",GreaterGreater:"⪢",GreaterLess:"≷",GreaterSlantEqual:"⩾",GreaterTilde:"≳",Gscr:"\uD835\uDCA2",Gt:"≫",HARDcy:"Ъ",Hacek:"ˇ",Hat:"^",Hcirc:"Ĥ",Hfr:"ℌ",HilbertSpace:"ℋ",Hopf:"ℍ",HorizontalLine:"─",Hscr:"ℋ",Hstrok:"Ħ",HumpDownHump:"≎",HumpEqual:"≏",IEcy:"Е",IJlig:"Ĳ",IOcy:"Ё",Iacute:"\xcd",Icirc:"\xce",Icy:"И",Idot:"İ",Ifr:"ℑ",Igrave:"\xcc",Im:"ℑ",Imacr:"Ī",ImaginaryI:"ⅈ",Implies:"⇒",Int:"∬",Integral:"∫",Intersection:"⋂",InvisibleComma:"⁣",InvisibleTimes:"⁢",Iogon:"Į",Iopf:"\uD835\uDD40",Iota:"Ι",Iscr:"ℐ",Itilde:"Ĩ",Iukcy:"І",Iuml:"\xcf",Jcirc:"Ĵ",Jcy:"Й",Jfr:"\uD835\uDD0D",Jopf:"\uD835\uDD41",Jscr:"\uD835\uDCA5",Jsercy:"Ј",Jukcy:"Є",KHcy:"Х",KJcy:"Ќ",Kappa:"Κ",Kcedil:"Ķ",Kcy:"К",Kfr:"\uD835\uDD0E",Kopf:"\uD835\uDD42",Kscr:"\uD835\uDCA6",LJcy:"Љ",LT:"<",Lacute:"Ĺ",Lambda:"Λ",Lang:"⟪",Laplacetrf:"ℒ",Larr:"↞",Lcaron:"Ľ",Lcedil:"Ļ",Lcy:"Л",LeftAngleBracket:"⟨",LeftArrow:"←",LeftArrowBar:"⇤",LeftArrowRightArrow:"⇆",LeftCeiling:"⌈",LeftDoubleBracket:"⟦",LeftDownTeeVector:"⥡",LeftDownVector:"⇃",LeftDownVectorBar:"⥙",LeftFloor:"⌊",LeftRightArrow:"↔",LeftRightVector:"⥎",LeftTee:"⊣",LeftTeeArrow:"↤",LeftTeeVector:"⥚",LeftTriangle:"⊲",LeftTriangleBar:"⧏",LeftTriangleEqual:"⊴",LeftUpDownVector:"⥑",LeftUpTeeVector:"⥠",LeftUpVector:"↿",LeftUpVectorBar:"⥘",LeftVector:"↼",LeftVectorBar:"⥒",Leftarrow:"⇐",Leftrightarrow:"⇔",LessEqualGreater:"⋚",LessFullEqual:"≦",LessGreater:"≶",LessLess:"⪡",LessSlantEqual:"⩽",LessTilde:"≲",Lfr:"\uD835\uDD0F",Ll:"⋘",Lleftarrow:"⇚",Lmidot:"Ŀ",LongLeftArrow:"⟵",LongLeftRightArrow:"⟷",LongRightArrow:"⟶",Longleftarrow:"⟸",Longleftrightarrow:"⟺",Longrightarrow:"⟹",Lopf:"\uD835\uDD43",LowerLeftArrow:"↙",LowerRightArrow:"↘",Lscr:"ℒ",Lsh:"↰",Lstrok:"Ł",Lt:"≪",Map:"⤅",Mcy:"М",MediumSpace:" ",Mellintrf:"ℳ",Mfr:"\uD835\uDD10",MinusPlus:"∓",Mopf:"\uD835\uDD44",Mscr:"ℳ",Mu:"Μ",NJcy:"Њ",Nacute:"Ń",Ncaron:"Ň",Ncedil:"Ņ",Ncy:"Н",NegativeMediumSpace:"​",NegativeThickSpace:"​",NegativeThinSpace:"​",NegativeVeryThinSpace:"​",NestedGreaterGreater:"≫",NestedLessLess:"≪",NewLine:"\n",Nfr:"\uD835\uDD11",NoBreak:"⁠",NonBreakingSpace:"\xa0",Nopf:"ℕ",Not:"⫬",NotCongruent:"≢",NotCupCap:"≭",NotDoubleVerticalBar:"∦",NotElement:"∉",NotEqual:"≠",NotEqualTilde:"≂̸",NotExists:"∄",NotGreater:"≯",NotGreaterEqual:"≱",NotGreaterFullEqual:"≧̸",NotGreaterGreater:"≫̸",NotGreaterLess:"≹",NotGreaterSlantEqual:"⩾̸",NotGreaterTilde:"≵",NotHumpDownHump:"≎̸",NotHumpEqual:"≏̸",NotLeftTriangle:"⋪",NotLeftTriangleBar:"⧏̸",NotLeftTriangleEqual:"⋬",NotLess:"≮",NotLessEqual:"≰",NotLessGreater:"≸",NotLessLess:"≪̸",NotLessSlantEqual:"⩽̸",NotLessTilde:"≴",NotNestedGreaterGreater:"⪢̸",NotNestedLessLess:"⪡̸",NotPrecedes:"⊀",NotPrecedesEqual:"⪯̸",NotPrecedesSlantEqual:"⋠",NotReverseElement:"∌",NotRightTriangle:"⋫",NotRightTriangleBar:"⧐̸",NotRightTriangleEqual:"⋭",NotSquareSubset:"⊏̸",NotSquareSubsetEqual:"⋢",NotSquareSuperset:"⊐̸",NotSquareSupersetEqual:"⋣",NotSubset:"⊂⃒",NotSubsetEqual:"⊈",NotSucceeds:"⊁",NotSucceedsEqual:"⪰̸",NotSucceedsSlantEqual:"⋡",NotSucceedsTilde:"≿̸",NotSuperset:"⊃⃒",NotSupersetEqual:"⊉",NotTilde:"≁",NotTildeEqual:"≄",NotTildeFullEqual:"≇",NotTildeTilde:"≉",NotVerticalBar:"∤",Nscr:"\uD835\uDCA9",Ntilde:"\xd1",Nu:"Ν",OElig:"Œ",Oacute:"\xd3",Ocirc:"\xd4",Ocy:"О",Odblac:"Ő",Ofr:"\uD835\uDD12",Ograve:"\xd2",Omacr:"Ō",Omega:"Ω",Omicron:"Ο",Oopf:"\uD835\uDD46",OpenCurlyDoubleQuote:"“",OpenCurlyQuote:"‘",Or:"⩔",Oscr:"\uD835\uDCAA",Oslash:"\xd8",Otilde:"\xd5",Otimes:"⨷",Ouml:"\xd6",OverBar:"‾",OverBrace:"⏞",OverBracket:"⎴",OverParenthesis:"⏜",PartialD:"∂",Pcy:"П",Pfr:"\uD835\uDD13",Phi:"Φ",Pi:"Π",PlusMinus:"\xb1",Poincareplane:"ℌ",Popf:"ℙ",Pr:"⪻",Precedes:"≺",PrecedesEqual:"⪯",PrecedesSlantEqual:"≼",PrecedesTilde:"≾",Prime:"″",Product:"∏",Proportion:"∷",Proportional:"∝",Pscr:"\uD835\uDCAB",Psi:"Ψ",QUOT:'"',Qfr:"\uD835\uDD14",Qopf:"ℚ",Qscr:"\uD835\uDCAC",RBarr:"⤐",REG:"\xae",Racute:"Ŕ",Rang:"⟫",Rarr:"↠",Rarrtl:"⤖",Rcaron:"Ř",Rcedil:"Ŗ",Rcy:"Р",Re:"ℜ",ReverseElement:"∋",ReverseEquilibrium:"⇋",ReverseUpEquilibrium:"⥯",Rfr:"ℜ",Rho:"Ρ",RightAngleBracket:"⟩",RightArrow:"→",RightArrowBar:"⇥",RightArrowLeftArrow:"⇄",RightCeiling:"⌉",RightDoubleBracket:"⟧",RightDownTeeVector:"⥝",RightDownVector:"⇂",RightDownVectorBar:"⥕",RightFloor:"⌋",RightTee:"⊢",RightTeeArrow:"↦",RightTeeVector:"⥛",RightTriangle:"⊳",RightTriangleBar:"⧐",RightTriangleEqual:"⊵",RightUpDownVector:"⥏",RightUpTeeVector:"⥜",RightUpVector:"↾",RightUpVectorBar:"⥔",RightVector:"⇀",RightVectorBar:"⥓",Rightarrow:"⇒",Ropf:"ℝ",RoundImplies:"⥰",Rrightarrow:"⇛",Rscr:"ℛ",Rsh:"↱",RuleDelayed:"⧴",SHCHcy:"Щ",SHcy:"Ш",SOFTcy:"Ь",Sacute:"Ś",Sc:"⪼",Scaron:"Š",Scedil:"Ş",Scirc:"Ŝ",Scy:"С",Sfr:"\uD835\uDD16",ShortDownArrow:"↓",ShortLeftArrow:"←",ShortRightArrow:"→",ShortUpArrow:"↑",Sigma:"Σ",SmallCircle:"∘",Sopf:"\uD835\uDD4A",Sqrt:"√",Square:"□",SquareIntersection:"⊓",SquareSubset:"⊏",SquareSubsetEqual:"⊑",SquareSuperset:"⊐",SquareSupersetEqual:"⊒",SquareUnion:"⊔",Sscr:"\uD835\uDCAE",Star:"⋆",Sub:"⋐",Subset:"⋐",SubsetEqual:"⊆",Succeeds:"≻",SucceedsEqual:"⪰",SucceedsSlantEqual:"≽",SucceedsTilde:"≿",SuchThat:"∋",Sum:"∑",Sup:"⋑",Superset:"⊃",SupersetEqual:"⊇",Supset:"⋑",THORN:"\xde",TRADE:"™",TSHcy:"Ћ",TScy:"Ц",Tab:"	",Tau:"Τ",Tcaron:"Ť",Tcedil:"Ţ",Tcy:"Т",Tfr:"\uD835\uDD17",Therefore:"∴",Theta:"Θ",ThickSpace:"  ",ThinSpace:" ",Tilde:"∼",TildeEqual:"≃",TildeFullEqual:"≅",TildeTilde:"≈",Topf:"\uD835\uDD4B",TripleDot:"⃛",Tscr:"\uD835\uDCAF",Tstrok:"Ŧ",Uacute:"\xda",Uarr:"↟",Uarrocir:"⥉",Ubrcy:"Ў",Ubreve:"Ŭ",Ucirc:"\xdb",Ucy:"У",Udblac:"Ű",Ufr:"\uD835\uDD18",Ugrave:"\xd9",Umacr:"Ū",UnderBar:"_",UnderBrace:"⏟",UnderBracket:"⎵",UnderParenthesis:"⏝",Union:"⋃",UnionPlus:"⊎",Uogon:"Ų",Uopf:"\uD835\uDD4C",UpArrow:"↑",UpArrowBar:"⤒",UpArrowDownArrow:"⇅",UpDownArrow:"↕",UpEquilibrium:"⥮",UpTee:"⊥",UpTeeArrow:"↥",Uparrow:"⇑",Updownarrow:"⇕",UpperLeftArrow:"↖",UpperRightArrow:"↗",Upsi:"ϒ",Upsilon:"Υ",Uring:"Ů",Uscr:"\uD835\uDCB0",Utilde:"Ũ",Uuml:"\xdc",VDash:"⊫",Vbar:"⫫",Vcy:"В",Vdash:"⊩",Vdashl:"⫦",Vee:"⋁",Verbar:"‖",Vert:"‖",VerticalBar:"∣",VerticalLine:"|",VerticalSeparator:"❘",VerticalTilde:"≀",VeryThinSpace:" ",Vfr:"\uD835\uDD19",Vopf:"\uD835\uDD4D",Vscr:"\uD835\uDCB1",Vvdash:"⊪",Wcirc:"Ŵ",Wedge:"⋀",Wfr:"\uD835\uDD1A",Wopf:"\uD835\uDD4E",Wscr:"\uD835\uDCB2",Xfr:"\uD835\uDD1B",Xi:"Ξ",Xopf:"\uD835\uDD4F",Xscr:"\uD835\uDCB3",YAcy:"Я",YIcy:"Ї",YUcy:"Ю",Yacute:"\xdd",Ycirc:"Ŷ",Ycy:"Ы",Yfr:"\uD835\uDD1C",Yopf:"\uD835\uDD50",Yscr:"\uD835\uDCB4",Yuml:"Ÿ",ZHcy:"Ж",Zacute:"Ź",Zcaron:"Ž",Zcy:"З",Zdot:"Ż",ZeroWidthSpace:"​",Zeta:"Ζ",Zfr:"ℨ",Zopf:"ℤ",Zscr:"\uD835\uDCB5",aacute:"\xe1",abreve:"ă",ac:"∾",acE:"∾̳",acd:"∿",acirc:"\xe2",acute:"\xb4",acy:"а",aelig:"\xe6",af:"⁡",afr:"\uD835\uDD1E",agrave:"\xe0",alefsym:"ℵ",aleph:"ℵ",alpha:"α",amacr:"ā",amalg:"⨿",amp:"&",and:"∧",andand:"⩕",andd:"⩜",andslope:"⩘",andv:"⩚",ang:"∠",ange:"⦤",angle:"∠",angmsd:"∡",angmsdaa:"⦨",angmsdab:"⦩",angmsdac:"⦪",angmsdad:"⦫",angmsdae:"⦬",angmsdaf:"⦭",angmsdag:"⦮",angmsdah:"⦯",angrt:"∟",angrtvb:"⊾",angrtvbd:"⦝",angsph:"∢",angst:"\xc5",angzarr:"⍼",aogon:"ą",aopf:"\uD835\uDD52",ap:"≈",apE:"⩰",apacir:"⩯",ape:"≊",apid:"≋",apos:"'",approx:"≈",approxeq:"≊",aring:"\xe5",ascr:"\uD835\uDCB6",ast:"*",asymp:"≈",asympeq:"≍",atilde:"\xe3",auml:"\xe4",awconint:"∳",awint:"⨑",bNot:"⫭",backcong:"≌",backepsilon:"϶",backprime:"‵",backsim:"∽",backsimeq:"⋍",barvee:"⊽",barwed:"⌅",barwedge:"⌅",bbrk:"⎵",bbrktbrk:"⎶",bcong:"≌",bcy:"б",bdquo:"„",becaus:"∵",because:"∵",bemptyv:"⦰",bepsi:"϶",bernou:"ℬ",beta:"β",beth:"ℶ",between:"≬",bfr:"\uD835\uDD1F",bigcap:"⋂",bigcirc:"◯",bigcup:"⋃",bigodot:"⨀",bigoplus:"⨁",bigotimes:"⨂",bigsqcup:"⨆",bigstar:"★",bigtriangledown:"▽",bigtriangleup:"△",biguplus:"⨄",bigvee:"⋁",bigwedge:"⋀",bkarow:"⤍",blacklozenge:"⧫",blacksquare:"▪",blacktriangle:"▴",blacktriangledown:"▾",blacktriangleleft:"◂",blacktriangleright:"▸",blank:"␣",blk12:"▒",blk14:"░",blk34:"▓",block:"█",bne:"=⃥",bnequiv:"≡⃥",bnot:"⌐",bopf:"\uD835\uDD53",bot:"⊥",bottom:"⊥",bowtie:"⋈",boxDL:"╗",boxDR:"╔",boxDl:"╖",boxDr:"╓",boxH:"═",boxHD:"╦",boxHU:"╩",boxHd:"╤",boxHu:"╧",boxUL:"╝",boxUR:"╚",boxUl:"╜",boxUr:"╙",boxV:"║",boxVH:"╬",boxVL:"╣",boxVR:"╠",boxVh:"╫",boxVl:"╢",boxVr:"╟",boxbox:"⧉",boxdL:"╕",boxdR:"╒",boxdl:"┐",boxdr:"┌",boxh:"─",boxhD:"╥",boxhU:"╨",boxhd:"┬",boxhu:"┴",boxminus:"⊟",boxplus:"⊞",boxtimes:"⊠",boxuL:"╛",boxuR:"╘",boxul:"┘",boxur:"└",boxv:"│",boxvH:"╪",boxvL:"╡",boxvR:"╞",boxvh:"┼",boxvl:"┤",boxvr:"├",bprime:"‵",breve:"˘",brvbar:"\xa6",bscr:"\uD835\uDCB7",bsemi:"⁏",bsim:"∽",bsime:"⋍",bsol:"\\",bsolb:"⧅",bsolhsub:"⟈",bull:"•",bullet:"•",bump:"≎",bumpE:"⪮",bumpe:"≏",bumpeq:"≏",cacute:"ć",cap:"∩",capand:"⩄",capbrcup:"⩉",capcap:"⩋",capcup:"⩇",capdot:"⩀",caps:"∩︀",caret:"⁁",caron:"ˇ",ccaps:"⩍",ccaron:"č",ccedil:"\xe7",ccirc:"ĉ",ccups:"⩌",ccupssm:"⩐",cdot:"ċ",cedil:"\xb8",cemptyv:"⦲",cent:"\xa2",centerdot:"\xb7",cfr:"\uD835\uDD20",chcy:"ч",check:"✓",checkmark:"✓",chi:"χ",cir:"○",cirE:"⧃",circ:"ˆ",circeq:"≗",circlearrowleft:"↺",circlearrowright:"↻",circledR:"\xae",circledS:"Ⓢ",circledast:"⊛",circledcirc:"⊚",circleddash:"⊝",cire:"≗",cirfnint:"⨐",cirmid:"⫯",cirscir:"⧂",clubs:"♣",clubsuit:"♣",colon:":",colone:"≔",coloneq:"≔",comma:",",commat:"@",comp:"∁",compfn:"∘",complement:"∁",complexes:"ℂ",cong:"≅",congdot:"⩭",conint:"∮",copf:"\uD835\uDD54",coprod:"∐",copy:"\xa9",copysr:"℗",crarr:"↵",cross:"✗",cscr:"\uD835\uDCB8",csub:"⫏",csube:"⫑",csup:"⫐",csupe:"⫒",ctdot:"⋯",cudarrl:"⤸",cudarrr:"⤵",cuepr:"⋞",cuesc:"⋟",cularr:"↶",cularrp:"⤽",cup:"∪",cupbrcap:"⩈",cupcap:"⩆",cupcup:"⩊",cupdot:"⊍",cupor:"⩅",cups:"∪︀",curarr:"↷",curarrm:"⤼",curlyeqprec:"⋞",curlyeqsucc:"⋟",curlyvee:"⋎",curlywedge:"⋏",curren:"\xa4",curvearrowleft:"↶",curvearrowright:"↷",cuvee:"⋎",cuwed:"⋏",cwconint:"∲",cwint:"∱",cylcty:"⌭",dArr:"⇓",dHar:"⥥",dagger:"†",daleth:"ℸ",darr:"↓",dash:"‐",dashv:"⊣",dbkarow:"⤏",dblac:"˝",dcaron:"ď",dcy:"д",dd:"ⅆ",ddagger:"‡",ddarr:"⇊",ddotseq:"⩷",deg:"\xb0",delta:"δ",demptyv:"⦱",dfisht:"⥿",dfr:"\uD835\uDD21",dharl:"⇃",dharr:"⇂",diam:"⋄",diamond:"⋄",diamondsuit:"♦",diams:"♦",die:"\xa8",digamma:"ϝ",disin:"⋲",div:"\xf7",divide:"\xf7",divideontimes:"⋇",divonx:"⋇",djcy:"ђ",dlcorn:"⌞",dlcrop:"⌍",dollar:"$",dopf:"\uD835\uDD55",dot:"˙",doteq:"≐",doteqdot:"≑",dotminus:"∸",dotplus:"∔",dotsquare:"⊡",doublebarwedge:"⌆",downarrow:"↓",downdownarrows:"⇊",downharpoonleft:"⇃",downharpoonright:"⇂",drbkarow:"⤐",drcorn:"⌟",drcrop:"⌌",dscr:"\uD835\uDCB9",dscy:"ѕ",dsol:"⧶",dstrok:"đ",dtdot:"⋱",dtri:"▿",dtrif:"▾",duarr:"⇵",duhar:"⥯",dwangle:"⦦",dzcy:"џ",dzigrarr:"⟿",eDDot:"⩷",eDot:"≑",eacute:"\xe9",easter:"⩮",ecaron:"ě",ecir:"≖",ecirc:"\xea",ecolon:"≕",ecy:"э",edot:"ė",ee:"ⅇ",efDot:"≒",efr:"\uD835\uDD22",eg:"⪚",egrave:"\xe8",egs:"⪖",egsdot:"⪘",el:"⪙",elinters:"⏧",ell:"ℓ",els:"⪕",elsdot:"⪗",emacr:"ē",empty:"∅",emptyset:"∅",emptyv:"∅",emsp13:" ",emsp14:" ",emsp:" ",eng:"ŋ",ensp:" ",eogon:"ę",eopf:"\uD835\uDD56",epar:"⋕",eparsl:"⧣",eplus:"⩱",epsi:"ε",epsilon:"ε",epsiv:"ϵ",eqcirc:"≖",eqcolon:"≕",eqsim:"≂",eqslantgtr:"⪖",eqslantless:"⪕",equals:"=",equest:"≟",equiv:"≡",equivDD:"⩸",eqvparsl:"⧥",erDot:"≓",erarr:"⥱",escr:"ℯ",esdot:"≐",esim:"≂",eta:"η",eth:"\xf0",euml:"\xeb",euro:"€",excl:"!",exist:"∃",expectation:"ℰ",exponentiale:"ⅇ",fallingdotseq:"≒",fcy:"ф",female:"♀",ffilig:"ﬃ",fflig:"ﬀ",ffllig:"ﬄ",ffr:"\uD835\uDD23",filig:"ﬁ",fjlig:"fj",flat:"♭",fllig:"ﬂ",fltns:"▱",fnof:"ƒ",fopf:"\uD835\uDD57",forall:"∀",fork:"⋔",forkv:"⫙",fpartint:"⨍",frac12:"\xbd",frac13:"⅓",frac14:"\xbc",frac15:"⅕",frac16:"⅙",frac18:"⅛",frac23:"⅔",frac25:"⅖",frac34:"\xbe",frac35:"⅗",frac38:"⅜",frac45:"⅘",frac56:"⅚",frac58:"⅝",frac78:"⅞",frasl:"⁄",frown:"⌢",fscr:"\uD835\uDCBB",gE:"≧",gEl:"⪌",gacute:"ǵ",gamma:"γ",gammad:"ϝ",gap:"⪆",gbreve:"ğ",gcirc:"ĝ",gcy:"г",gdot:"ġ",ge:"≥",gel:"⋛",geq:"≥",geqq:"≧",geqslant:"⩾",ges:"⩾",gescc:"⪩",gesdot:"⪀",gesdoto:"⪂",gesdotol:"⪄",gesl:"⋛︀",gesles:"⪔",gfr:"\uD835\uDD24",gg:"≫",ggg:"⋙",gimel:"ℷ",gjcy:"ѓ",gl:"≷",glE:"⪒",gla:"⪥",glj:"⪤",gnE:"≩",gnap:"⪊",gnapprox:"⪊",gne:"⪈",gneq:"⪈",gneqq:"≩",gnsim:"⋧",gopf:"\uD835\uDD58",grave:"`",gscr:"ℊ",gsim:"≳",gsime:"⪎",gsiml:"⪐",gt:">",gtcc:"⪧",gtcir:"⩺",gtdot:"⋗",gtlPar:"⦕",gtquest:"⩼",gtrapprox:"⪆",gtrarr:"⥸",gtrdot:"⋗",gtreqless:"⋛",gtreqqless:"⪌",gtrless:"≷",gtrsim:"≳",gvertneqq:"≩︀",gvnE:"≩︀",hArr:"⇔",hairsp:" ",half:"\xbd",hamilt:"ℋ",hardcy:"ъ",harr:"↔",harrcir:"⥈",harrw:"↭",hbar:"ℏ",hcirc:"ĥ",hearts:"♥",heartsuit:"♥",hellip:"…",hercon:"⊹",hfr:"\uD835\uDD25",hksearow:"⤥",hkswarow:"⤦",hoarr:"⇿",homtht:"∻",hookleftarrow:"↩",hookrightarrow:"↪",hopf:"\uD835\uDD59",horbar:"―",hscr:"\uD835\uDCBD",hslash:"ℏ",hstrok:"ħ",hybull:"⁃",hyphen:"‐",iacute:"\xed",ic:"⁣",icirc:"\xee",icy:"и",iecy:"е",iexcl:"\xa1",iff:"⇔",ifr:"\uD835\uDD26",igrave:"\xec",ii:"ⅈ",iiiint:"⨌",iiint:"∭",iinfin:"⧜",iiota:"℩",ijlig:"ĳ",imacr:"ī",image:"ℑ",imagline:"ℐ",imagpart:"ℑ",imath:"ı",imof:"⊷",imped:"Ƶ",in:"∈",incare:"℅",infin:"∞",infintie:"⧝",inodot:"ı",int:"∫",intcal:"⊺",integers:"ℤ",intercal:"⊺",intlarhk:"⨗",intprod:"⨼",iocy:"ё",iogon:"į",iopf:"\uD835\uDD5A",iota:"ι",iprod:"⨼",iquest:"\xbf",iscr:"\uD835\uDCBE",isin:"∈",isinE:"⋹",isindot:"⋵",isins:"⋴",isinsv:"⋳",isinv:"∈",it:"⁢",itilde:"ĩ",iukcy:"і",iuml:"\xef",jcirc:"ĵ",jcy:"й",jfr:"\uD835\uDD27",jmath:"ȷ",jopf:"\uD835\uDD5B",jscr:"\uD835\uDCBF",jsercy:"ј",jukcy:"є",kappa:"κ",kappav:"ϰ",kcedil:"ķ",kcy:"к",kfr:"\uD835\uDD28",kgreen:"ĸ",khcy:"х",kjcy:"ќ",kopf:"\uD835\uDD5C",kscr:"\uD835\uDCC0",lAarr:"⇚",lArr:"⇐",lAtail:"⤛",lBarr:"⤎",lE:"≦",lEg:"⪋",lHar:"⥢",lacute:"ĺ",laemptyv:"⦴",lagran:"ℒ",lambda:"λ",lang:"⟨",langd:"⦑",langle:"⟨",lap:"⪅",laquo:"\xab",larr:"←",larrb:"⇤",larrbfs:"⤟",larrfs:"⤝",larrhk:"↩",larrlp:"↫",larrpl:"⤹",larrsim:"⥳",larrtl:"↢",lat:"⪫",latail:"⤙",late:"⪭",lates:"⪭︀",lbarr:"⤌",lbbrk:"❲",lbrace:"{",lbrack:"[",lbrke:"⦋",lbrksld:"⦏",lbrkslu:"⦍",lcaron:"ľ",lcedil:"ļ",lceil:"⌈",lcub:"{",lcy:"л",ldca:"⤶",ldquo:"“",ldquor:"„",ldrdhar:"⥧",ldrushar:"⥋",ldsh:"↲",le:"≤",leftarrow:"←",leftarrowtail:"↢",leftharpoondown:"↽",leftharpoonup:"↼",leftleftarrows:"⇇",leftrightarrow:"↔",leftrightarrows:"⇆",leftrightharpoons:"⇋",leftrightsquigarrow:"↭",leftthreetimes:"⋋",leg:"⋚",leq:"≤",leqq:"≦",leqslant:"⩽",les:"⩽",lescc:"⪨",lesdot:"⩿",lesdoto:"⪁",lesdotor:"⪃",lesg:"⋚︀",lesges:"⪓",lessapprox:"⪅",lessdot:"⋖",lesseqgtr:"⋚",lesseqqgtr:"⪋",lessgtr:"≶",lesssim:"≲",lfisht:"⥼",lfloor:"⌊",lfr:"\uD835\uDD29",lg:"≶",lgE:"⪑",lhard:"↽",lharu:"↼",lharul:"⥪",lhblk:"▄",ljcy:"љ",ll:"≪",llarr:"⇇",llcorner:"⌞",llhard:"⥫",lltri:"◺",lmidot:"ŀ",lmoust:"⎰",lmoustache:"⎰",lnE:"≨",lnap:"⪉",lnapprox:"⪉",lne:"⪇",lneq:"⪇",lneqq:"≨",lnsim:"⋦",loang:"⟬",loarr:"⇽",lobrk:"⟦",longleftarrow:"⟵",longleftrightarrow:"⟷",longmapsto:"⟼",longrightarrow:"⟶",looparrowleft:"↫",looparrowright:"↬",lopar:"⦅",lopf:"\uD835\uDD5D",loplus:"⨭",lotimes:"⨴",lowast:"∗",lowbar:"_",loz:"◊",lozenge:"◊",lozf:"⧫",lpar:"(",lparlt:"⦓",lrarr:"⇆",lrcorner:"⌟",lrhar:"⇋",lrhard:"⥭",lrm:"‎",lrtri:"⊿",lsaquo:"‹",lscr:"\uD835\uDCC1",lsh:"↰",lsim:"≲",lsime:"⪍",lsimg:"⪏",lsqb:"[",lsquo:"‘",lsquor:"‚",lstrok:"ł",lt:"<",ltcc:"⪦",ltcir:"⩹",ltdot:"⋖",lthree:"⋋",ltimes:"⋉",ltlarr:"⥶",ltquest:"⩻",ltrPar:"⦖",ltri:"◃",ltrie:"⊴",ltrif:"◂",lurdshar:"⥊",luruhar:"⥦",lvertneqq:"≨︀",lvnE:"≨︀",mDDot:"∺",macr:"\xaf",male:"♂",malt:"✠",maltese:"✠",map:"↦",mapsto:"↦",mapstodown:"↧",mapstoleft:"↤",mapstoup:"↥",marker:"▮",mcomma:"⨩",mcy:"м",mdash:"—",measuredangle:"∡",mfr:"\uD835\uDD2A",mho:"℧",micro:"\xb5",mid:"∣",midast:"*",midcir:"⫰",middot:"\xb7",minus:"−",minusb:"⊟",minusd:"∸",minusdu:"⨪",mlcp:"⫛",mldr:"…",mnplus:"∓",models:"⊧",mopf:"\uD835\uDD5E",mp:"∓",mscr:"\uD835\uDCC2",mstpos:"∾",mu:"μ",multimap:"⊸",mumap:"⊸",nGg:"⋙̸",nGt:"≫⃒",nGtv:"≫̸",nLeftarrow:"⇍",nLeftrightarrow:"⇎",nLl:"⋘̸",nLt:"≪⃒",nLtv:"≪̸",nRightarrow:"⇏",nVDash:"⊯",nVdash:"⊮",nabla:"∇",nacute:"ń",nang:"∠⃒",nap:"≉",napE:"⩰̸",napid:"≋̸",napos:"ŉ",napprox:"≉",natur:"♮",natural:"♮",naturals:"ℕ",nbsp:"\xa0",nbump:"≎̸",nbumpe:"≏̸",ncap:"⩃",ncaron:"ň",ncedil:"ņ",ncong:"≇",ncongdot:"⩭̸",ncup:"⩂",ncy:"н",ndash:"–",ne:"≠",neArr:"⇗",nearhk:"⤤",nearr:"↗",nearrow:"↗",nedot:"≐̸",nequiv:"≢",nesear:"⤨",nesim:"≂̸",nexist:"∄",nexists:"∄",nfr:"\uD835\uDD2B",ngE:"≧̸",nge:"≱",ngeq:"≱",ngeqq:"≧̸",ngeqslant:"⩾̸",nges:"⩾̸",ngsim:"≵",ngt:"≯",ngtr:"≯",nhArr:"⇎",nharr:"↮",nhpar:"⫲",ni:"∋",nis:"⋼",nisd:"⋺",niv:"∋",njcy:"њ",nlArr:"⇍",nlE:"≦̸",nlarr:"↚",nldr:"‥",nle:"≰",nleftarrow:"↚",nleftrightarrow:"↮",nleq:"≰",nleqq:"≦̸",nleqslant:"⩽̸",nles:"⩽̸",nless:"≮",nlsim:"≴",nlt:"≮",nltri:"⋪",nltrie:"⋬",nmid:"∤",nopf:"\uD835\uDD5F",not:"\xac",notin:"∉",notinE:"⋹̸",notindot:"⋵̸",notinva:"∉",notinvb:"⋷",notinvc:"⋶",notni:"∌",notniva:"∌",notnivb:"⋾",notnivc:"⋽",npar:"∦",nparallel:"∦",nparsl:"⫽⃥",npart:"∂̸",npolint:"⨔",npr:"⊀",nprcue:"⋠",npre:"⪯̸",nprec:"⊀",npreceq:"⪯̸",nrArr:"⇏",nrarr:"↛",nrarrc:"⤳̸",nrarrw:"↝̸",nrightarrow:"↛",nrtri:"⋫",nrtrie:"⋭",nsc:"⊁",nsccue:"⋡",nsce:"⪰̸",nscr:"\uD835\uDCC3",nshortmid:"∤",nshortparallel:"∦",nsim:"≁",nsime:"≄",nsimeq:"≄",nsmid:"∤",nspar:"∦",nsqsube:"⋢",nsqsupe:"⋣",nsub:"⊄",nsubE:"⫅̸",nsube:"⊈",nsubset:"⊂⃒",nsubseteq:"⊈",nsubseteqq:"⫅̸",nsucc:"⊁",nsucceq:"⪰̸",nsup:"⊅",nsupE:"⫆̸",nsupe:"⊉",nsupset:"⊃⃒",nsupseteq:"⊉",nsupseteqq:"⫆̸",ntgl:"≹",ntilde:"\xf1",ntlg:"≸",ntriangleleft:"⋪",ntrianglelefteq:"⋬",ntriangleright:"⋫",ntrianglerighteq:"⋭",nu:"ν",num:"#",numero:"№",numsp:" ",nvDash:"⊭",nvHarr:"⤄",nvap:"≍⃒",nvdash:"⊬",nvge:"≥⃒",nvgt:">⃒",nvinfin:"⧞",nvlArr:"⤂",nvle:"≤⃒",nvlt:"<⃒",nvltrie:"⊴⃒",nvrArr:"⤃",nvrtrie:"⊵⃒",nvsim:"∼⃒",nwArr:"⇖",nwarhk:"⤣",nwarr:"↖",nwarrow:"↖",nwnear:"⤧",oS:"Ⓢ",oacute:"\xf3",oast:"⊛",ocir:"⊚",ocirc:"\xf4",ocy:"о",odash:"⊝",odblac:"ő",odiv:"⨸",odot:"⊙",odsold:"⦼",oelig:"œ",ofcir:"⦿",ofr:"\uD835\uDD2C",ogon:"˛",ograve:"\xf2",ogt:"⧁",ohbar:"⦵",ohm:"Ω",oint:"∮",olarr:"↺",olcir:"⦾",olcross:"⦻",oline:"‾",olt:"⧀",omacr:"ō",omega:"ω",omicron:"ο",omid:"⦶",ominus:"⊖",oopf:"\uD835\uDD60",opar:"⦷",operp:"⦹",oplus:"⊕",or:"∨",orarr:"↻",ord:"⩝",order:"ℴ",orderof:"ℴ",ordf:"\xaa",ordm:"\xba",origof:"⊶",oror:"⩖",orslope:"⩗",orv:"⩛",oscr:"ℴ",oslash:"\xf8",osol:"⊘",otilde:"\xf5",otimes:"⊗",otimesas:"⨶",ouml:"\xf6",ovbar:"⌽",par:"∥",para:"\xb6",parallel:"∥",parsim:"⫳",parsl:"⫽",part:"∂",pcy:"п",percnt:"%",period:".",permil:"‰",perp:"⊥",pertenk:"‱",pfr:"\uD835\uDD2D",phi:"φ",phiv:"ϕ",phmmat:"ℳ",phone:"☎",pi:"π",pitchfork:"⋔",piv:"ϖ",planck:"ℏ",planckh:"ℎ",plankv:"ℏ",plus:"+",plusacir:"⨣",plusb:"⊞",pluscir:"⨢",plusdo:"∔",plusdu:"⨥",pluse:"⩲",plusmn:"\xb1",plussim:"⨦",plustwo:"⨧",pm:"\xb1",pointint:"⨕",popf:"\uD835\uDD61",pound:"\xa3",pr:"≺",prE:"⪳",prap:"⪷",prcue:"≼",pre:"⪯",prec:"≺",precapprox:"⪷",preccurlyeq:"≼",preceq:"⪯",precnapprox:"⪹",precneqq:"⪵",precnsim:"⋨",precsim:"≾",prime:"′",primes:"ℙ",prnE:"⪵",prnap:"⪹",prnsim:"⋨",prod:"∏",profalar:"⌮",profline:"⌒",profsurf:"⌓",prop:"∝",propto:"∝",prsim:"≾",prurel:"⊰",pscr:"\uD835\uDCC5",psi:"ψ",puncsp:" ",qfr:"\uD835\uDD2E",qint:"⨌",qopf:"\uD835\uDD62",qprime:"⁗",qscr:"\uD835\uDCC6",quaternions:"ℍ",quatint:"⨖",quest:"?",questeq:"≟",quot:'"',rAarr:"⇛",rArr:"⇒",rAtail:"⤜",rBarr:"⤏",rHar:"⥤",race:"∽̱",racute:"ŕ",radic:"√",raemptyv:"⦳",rang:"⟩",rangd:"⦒",range:"⦥",rangle:"⟩",raquo:"\xbb",rarr:"→",rarrap:"⥵",rarrb:"⇥",rarrbfs:"⤠",rarrc:"⤳",rarrfs:"⤞",rarrhk:"↪",rarrlp:"↬",rarrpl:"⥅",rarrsim:"⥴",rarrtl:"↣",rarrw:"↝",ratail:"⤚",ratio:"∶",rationals:"ℚ",rbarr:"⤍",rbbrk:"❳",rbrace:"}",rbrack:"]",rbrke:"⦌",rbrksld:"⦎",rbrkslu:"⦐",rcaron:"ř",rcedil:"ŗ",rceil:"⌉",rcub:"}",rcy:"р",rdca:"⤷",rdldhar:"⥩",rdquo:"”",rdquor:"”",rdsh:"↳",real:"ℜ",realine:"ℛ",realpart:"ℜ",reals:"ℝ",rect:"▭",reg:"\xae",rfisht:"⥽",rfloor:"⌋",rfr:"\uD835\uDD2F",rhard:"⇁",rharu:"⇀",rharul:"⥬",rho:"ρ",rhov:"ϱ",rightarrow:"→",rightarrowtail:"↣",rightharpoondown:"⇁",rightharpoonup:"⇀",rightleftarrows:"⇄",rightleftharpoons:"⇌",rightrightarrows:"⇉",rightsquigarrow:"↝",rightthreetimes:"⋌",ring:"˚",risingdotseq:"≓",rlarr:"⇄",rlhar:"⇌",rlm:"‏",rmoust:"⎱",rmoustache:"⎱",rnmid:"⫮",roang:"⟭",roarr:"⇾",robrk:"⟧",ropar:"⦆",ropf:"\uD835\uDD63",roplus:"⨮",rotimes:"⨵",rpar:")",rpargt:"⦔",rppolint:"⨒",rrarr:"⇉",rsaquo:"›",rscr:"\uD835\uDCC7",rsh:"↱",rsqb:"]",rsquo:"’",rsquor:"’",rthree:"⋌",rtimes:"⋊",rtri:"▹",rtrie:"⊵",rtrif:"▸",rtriltri:"⧎",ruluhar:"⥨",rx:"℞",sacute:"ś",sbquo:"‚",sc:"≻",scE:"⪴",scap:"⪸",scaron:"š",sccue:"≽",sce:"⪰",scedil:"ş",scirc:"ŝ",scnE:"⪶",scnap:"⪺",scnsim:"⋩",scpolint:"⨓",scsim:"≿",scy:"с",sdot:"⋅",sdotb:"⊡",sdote:"⩦",seArr:"⇘",searhk:"⤥",searr:"↘",searrow:"↘",sect:"\xa7",semi:";",seswar:"⤩",setminus:"∖",setmn:"∖",sext:"✶",sfr:"\uD835\uDD30",sfrown:"⌢",sharp:"♯",shchcy:"щ",shcy:"ш",shortmid:"∣",shortparallel:"∥",shy:"\xad",sigma:"σ",sigmaf:"ς",sigmav:"ς",sim:"∼",simdot:"⩪",sime:"≃",simeq:"≃",simg:"⪞",simgE:"⪠",siml:"⪝",simlE:"⪟",simne:"≆",simplus:"⨤",simrarr:"⥲",slarr:"←",smallsetminus:"∖",smashp:"⨳",smeparsl:"⧤",smid:"∣",smile:"⌣",smt:"⪪",smte:"⪬",smtes:"⪬︀",softcy:"ь",sol:"/",solb:"⧄",solbar:"⌿",sopf:"\uD835\uDD64",spades:"♠",spadesuit:"♠",spar:"∥",sqcap:"⊓",sqcaps:"⊓︀",sqcup:"⊔",sqcups:"⊔︀",sqsub:"⊏",sqsube:"⊑",sqsubset:"⊏",sqsubseteq:"⊑",sqsup:"⊐",sqsupe:"⊒",sqsupset:"⊐",sqsupseteq:"⊒",squ:"□",square:"□",squarf:"▪",squf:"▪",srarr:"→",sscr:"\uD835\uDCC8",ssetmn:"∖",ssmile:"⌣",sstarf:"⋆",star:"☆",starf:"★",straightepsilon:"ϵ",straightphi:"ϕ",strns:"\xaf",sub:"⊂",subE:"⫅",subdot:"⪽",sube:"⊆",subedot:"⫃",submult:"⫁",subnE:"⫋",subne:"⊊",subplus:"⪿",subrarr:"⥹",subset:"⊂",subseteq:"⊆",subseteqq:"⫅",subsetneq:"⊊",subsetneqq:"⫋",subsim:"⫇",subsub:"⫕",subsup:"⫓",succ:"≻",succapprox:"⪸",succcurlyeq:"≽",succeq:"⪰",succnapprox:"⪺",succneqq:"⪶",succnsim:"⋩",succsim:"≿",sum:"∑",sung:"♪",sup1:"\xb9",sup2:"\xb2",sup3:"\xb3",sup:"⊃",supE:"⫆",supdot:"⪾",supdsub:"⫘",supe:"⊇",supedot:"⫄",suphsol:"⟉",suphsub:"⫗",suplarr:"⥻",supmult:"⫂",supnE:"⫌",supne:"⊋",supplus:"⫀",supset:"⊃",supseteq:"⊇",supseteqq:"⫆",supsetneq:"⊋",supsetneqq:"⫌",supsim:"⫈",supsub:"⫔",supsup:"⫖",swArr:"⇙",swarhk:"⤦",swarr:"↙",swarrow:"↙",swnwar:"⤪",szlig:"\xdf",target:"⌖",tau:"τ",tbrk:"⎴",tcaron:"ť",tcedil:"ţ",tcy:"т",tdot:"⃛",telrec:"⌕",tfr:"\uD835\uDD31",there4:"∴",therefore:"∴",theta:"θ",thetasym:"ϑ",thetav:"ϑ",thickapprox:"≈",thicksim:"∼",thinsp:" ",thkap:"≈",thksim:"∼",thorn:"\xfe",tilde:"˜",times:"\xd7",timesb:"⊠",timesbar:"⨱",timesd:"⨰",tint:"∭",toea:"⤨",top:"⊤",topbot:"⌶",topcir:"⫱",topf:"\uD835\uDD65",topfork:"⫚",tosa:"⤩",tprime:"‴",trade:"™",triangle:"▵",triangledown:"▿",triangleleft:"◃",trianglelefteq:"⊴",triangleq:"≜",triangleright:"▹",trianglerighteq:"⊵",tridot:"◬",trie:"≜",triminus:"⨺",triplus:"⨹",trisb:"⧍",tritime:"⨻",trpezium:"⏢",tscr:"\uD835\uDCC9",tscy:"ц",tshcy:"ћ",tstrok:"ŧ",twixt:"≬",twoheadleftarrow:"↞",twoheadrightarrow:"↠",uArr:"⇑",uHar:"⥣",uacute:"\xfa",uarr:"↑",ubrcy:"ў",ubreve:"ŭ",ucirc:"\xfb",ucy:"у",udarr:"⇅",udblac:"ű",udhar:"⥮",ufisht:"⥾",ufr:"\uD835\uDD32",ugrave:"\xf9",uharl:"↿",uharr:"↾",uhblk:"▀",ulcorn:"⌜",ulcorner:"⌜",ulcrop:"⌏",ultri:"◸",umacr:"ū",uml:"\xa8",uogon:"ų",uopf:"\uD835\uDD66",uparrow:"↑",updownarrow:"↕",upharpoonleft:"↿",upharpoonright:"↾",uplus:"⊎",upsi:"υ",upsih:"ϒ",upsilon:"υ",upuparrows:"⇈",urcorn:"⌝",urcorner:"⌝",urcrop:"⌎",uring:"ů",urtri:"◹",uscr:"\uD835\uDCCA",utdot:"⋰",utilde:"ũ",utri:"▵",utrif:"▴",uuarr:"⇈",uuml:"\xfc",uwangle:"⦧",vArr:"⇕",vBar:"⫨",vBarv:"⫩",vDash:"⊨",vangrt:"⦜",varepsilon:"ϵ",varkappa:"ϰ",varnothing:"∅",varphi:"ϕ",varpi:"ϖ",varpropto:"∝",varr:"↕",varrho:"ϱ",varsigma:"ς",varsubsetneq:"⊊︀",varsubsetneqq:"⫋︀",varsupsetneq:"⊋︀",varsupsetneqq:"⫌︀",vartheta:"ϑ",vartriangleleft:"⊲",vartriangleright:"⊳",vcy:"в",vdash:"⊢",vee:"∨",veebar:"⊻",veeeq:"≚",vellip:"⋮",verbar:"|",vert:"|",vfr:"\uD835\uDD33",vltri:"⊲",vnsub:"⊂⃒",vnsup:"⊃⃒",vopf:"\uD835\uDD67",vprop:"∝",vrtri:"⊳",vscr:"\uD835\uDCCB",vsubnE:"⫋︀",vsubne:"⊊︀",vsupnE:"⫌︀",vsupne:"⊋︀",vzigzag:"⦚",wcirc:"ŵ",wedbar:"⩟",wedge:"∧",wedgeq:"≙",weierp:"℘",wfr:"\uD835\uDD34",wopf:"\uD835\uDD68",wp:"℘",wr:"≀",wreath:"≀",wscr:"\uD835\uDCCC",xcap:"⋂",xcirc:"◯",xcup:"⋃",xdtri:"▽",xfr:"\uD835\uDD35",xhArr:"⟺",xharr:"⟷",xi:"ξ",xlArr:"⟸",xlarr:"⟵",xmap:"⟼",xnis:"⋻",xodot:"⨀",xopf:"\uD835\uDD69",xoplus:"⨁",xotime:"⨂",xrArr:"⟹",xrarr:"⟶",xscr:"\uD835\uDCCD",xsqcup:"⨆",xuplus:"⨄",xutri:"△",xvee:"⋁",xwedge:"⋀",yacute:"\xfd",yacy:"я",ycirc:"ŷ",ycy:"ы",yen:"\xa5",yfr:"\uD835\uDD36",yicy:"ї",yopf:"\uD835\uDD6A",yscr:"\uD835\uDCCE",yucy:"ю",yuml:"\xff",zacute:"ź",zcaron:"ž",zcy:"з",zdot:"ż",zeetrf:"ℨ",zeta:"ζ",zfr:"\uD835\uDD37",zhcy:"ж",zigrarr:"⇝",zopf:"\uD835\uDD6B",zscr:"\uD835\uDCCF",zwj:"‍",zwnj:"‌"},tD={}.hasOwnProperty;function tL(e){return!!tD.call(tE,e)&&tE[e]}let tT={name:"characterReference",tokenize:function(e,t,n){let r,i;let o=this,l=0;return function(t){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(t),e.exit("characterReferenceMarker"),a};function a(t){return 35===t?(e.enter("characterReferenceMarkerNumeric"),e.consume(t),e.exit("characterReferenceMarkerNumeric"),s):(e.enter("characterReferenceValue"),r=31,i=eZ,c(t))}function s(t){return 88===t||120===t?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(t),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),r=6,i=eK,c):(e.enter("characterReferenceValue"),r=7,i=eW,c(t))}function c(a){if(59===a&&l){let r=e.exit("characterReferenceValue");return i!==eZ||tL(o.sliceSerialize(r))?(e.enter("characterReferenceMarker"),e.consume(a),e.exit("characterReferenceMarker"),e.exit("characterReference"),t):n(a)}return i(a)&&l++<r?(e.consume(a),c):n(a)}}},tN={name:"characterEscape",tokenize:function(e,t,n){return function(t){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(t),e.exit("escapeMarker"),r};function r(r){return eY(r)?(e.enter("characterEscapeValue"),e.consume(r),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):n(r)}}},tM={name:"lineEnding",tokenize:function(e,t){return function(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),e3(e,t,"linePrefix")}}};function tI(e,t,n){let r=[],i=-1;for(;++i<e.length;){let o=e[i].resolveAll;o&&!r.includes(o)&&(t=o(t,n),r.push(o))}return t}let tP={name:"labelEnd",resolveAll:function(e){let t=-1,n=[];for(;++t<e.length;){let r=e[t][1];if(n.push(e[t]),"labelImage"===r.type||"labelLink"===r.type||"labelEnd"===r.type){let e="labelImage"===r.type?4:2;r.type="data",t+=e}}return e.length!==n.length&&eR(e,0,e.length,n),e},resolveTo:function(e,t){let n,r,i,o,l=e.length,a=0;for(;l--;)if(n=e[l][1],r){if("link"===n.type||"labelLink"===n.type&&n._inactive)break;"enter"===e[l][0]&&"labelLink"===n.type&&(n._inactive=!0)}else if(i){if("enter"===e[l][0]&&("labelImage"===n.type||"labelLink"===n.type)&&!n._balanced&&(r=l,"labelLink"!==n.type)){a=2;break}}else"labelEnd"===n.type&&(i=l);let s={type:"labelLink"===e[r][1].type?"link":"image",start:{...e[r][1].start},end:{...e[e.length-1][1].end}},c={type:"label",start:{...e[r][1].start},end:{...e[i][1].end}},u={type:"labelText",start:{...e[r+a+2][1].end},end:{...e[i-2][1].start}};return o=eO(o=[["enter",s,t],["enter",c,t]],e.slice(r+1,r+a+3)),o=eO(o,[["enter",u,t]]),o=eO(o,tI(t.parser.constructs.insideSpan.null,e.slice(r+a+4,i-3),t)),o=eO(o,[["exit",u,t],e[i-2],e[i-1],["exit",c,t]]),o=eO(o,e.slice(i+1)),o=eO(o,[["exit",s,t]]),eR(e,r,e.length,o),e},tokenize:function(e,t,n){let r,i;let o=this,l=o.events.length;for(;l--;)if(("labelImage"===o.events[l][1].type||"labelLink"===o.events[l][1].type)&&!o.events[l][1]._balanced){r=o.events[l][1];break}return function(t){return r?r._inactive?u(t):(i=o.parser.defined.includes(tg(o.sliceSerialize({start:r.end,end:o.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelEnd"),a):n(t)};function a(t){return 40===t?e.attempt(tz,c,i?c:u)(t):91===t?e.attempt(tA,c,i?s:u)(t):i?c(t):u(t)}function s(t){return e.attempt(tq,c,u)(t)}function c(e){return t(e)}function u(e){return r._balanced=!0,n(e)}}},tz={tokenize:function(e,t,n){return function(t){return e.enter("resource"),e.enter("resourceMarker"),e.consume(t),e.exit("resourceMarker"),r};function r(t){return eQ(t)?tm(e,i)(t):i(t)}function i(t){return 41===t?c(t):tp(e,o,l,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(t)}function o(t){return eQ(t)?tm(e,a)(t):c(t)}function l(e){return n(e)}function a(t){return 34===t||39===t||40===t?tf(e,s,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(t):c(t)}function s(t){return eQ(t)?tm(e,c)(t):c(t)}function c(r){return 41===r?(e.enter("resourceMarker"),e.consume(r),e.exit("resourceMarker"),e.exit("resource"),t):n(r)}}},tA={tokenize:function(e,t,n){let r=this;return function(t){return th.call(r,e,i,o,"reference","referenceMarker","referenceString")(t)};function i(e){return r.parser.defined.includes(tg(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?t(e):n(e)}function o(e){return n(e)}}},tq={tokenize:function(e,t,n){return function(t){return e.enter("reference"),e.enter("referenceMarker"),e.consume(t),e.exit("referenceMarker"),r};function r(r){return 93===r?(e.enter("referenceMarker"),e.consume(r),e.exit("referenceMarker"),e.exit("reference"),t):n(r)}}},tR={name:"labelStartImage",resolveAll:tP.resolveAll,tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(t),e.exit("labelImageMarker"),i};function i(t){return 91===t?(e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelImage"),o):n(t)}function o(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}};function tO(e){return null===e||eQ(e)||e0(e)?1:e1(e)?2:void 0}let tB={name:"attention",resolveAll:function(e,t){let n,r,i,o,l,a,s,c,u=-1;for(;++u<e.length;)if("enter"===e[u][0]&&"attentionSequence"===e[u][1].type&&e[u][1]._close){for(n=u;n--;)if("exit"===e[n][0]&&"attentionSequence"===e[n][1].type&&e[n][1]._open&&t.sliceSerialize(e[n][1]).charCodeAt(0)===t.sliceSerialize(e[u][1]).charCodeAt(0)){if((e[n][1]._close||e[u][1]._open)&&(e[u][1].end.offset-e[u][1].start.offset)%3&&!((e[n][1].end.offset-e[n][1].start.offset+e[u][1].end.offset-e[u][1].start.offset)%3))continue;a=e[n][1].end.offset-e[n][1].start.offset>1&&e[u][1].end.offset-e[u][1].start.offset>1?2:1;let d={...e[n][1].end},p={...e[u][1].start};tF(d,-a),tF(p,a),o={type:a>1?"strongSequence":"emphasisSequence",start:d,end:{...e[n][1].end}},l={type:a>1?"strongSequence":"emphasisSequence",start:{...e[u][1].start},end:p},i={type:a>1?"strongText":"emphasisText",start:{...e[n][1].end},end:{...e[u][1].start}},r={type:a>1?"strong":"emphasis",start:{...o.start},end:{...l.end}},e[n][1].end={...o.start},e[u][1].start={...l.end},s=[],e[n][1].end.offset-e[n][1].start.offset&&(s=eO(s,[["enter",e[n][1],t],["exit",e[n][1],t]])),s=eO(s,[["enter",r,t],["enter",o,t],["exit",o,t],["enter",i,t]]),s=eO(s,tI(t.parser.constructs.insideSpan.null,e.slice(n+1,u),t)),s=eO(s,[["exit",i,t],["enter",l,t],["exit",l,t],["exit",r,t]]),e[u][1].end.offset-e[u][1].start.offset?(c=2,s=eO(s,[["enter",e[u][1],t],["exit",e[u][1],t]])):c=0,eR(e,n-1,u-n+3,s),u=n+s.length-c-2;break}}for(u=-1;++u<e.length;)"attentionSequence"===e[u][1].type&&(e[u][1].type="data");return e},tokenize:function(e,t){let n;let r=this.parser.constructs.attentionMarkers.null,i=this.previous,o=tO(i);return function(l){return n=l,e.enter("attentionSequence"),function l(a){if(a===n)return e.consume(a),l;let s=e.exit("attentionSequence"),c=tO(a),u=!c||2===c&&o||r.includes(a),d=!o||2===o&&c||r.includes(i);return s._open=!!(42===n?u:u&&(o||!d)),s._close=!!(42===n?d:d&&(c||!u)),t(a)}(l)}}};function tF(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}let tH={name:"labelStartLink",resolveAll:tP.resolveAll,tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelLink"),i};function i(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}},tV={42:ts,43:ts,45:ts,48:ts,49:ts,50:ts,51:ts,52:ts,53:ts,54:ts,55:ts,56:ts,57:ts,62:td},tU={91:{name:"definition",tokenize:function(e,t,n){let r;let i=this;return function(t){return e.enter("definition"),th.call(i,e,o,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(t)};function o(t){return(r=tg(i.sliceSerialize(i.events[i.events.length-1][1]).slice(1,-1)),58===t)?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),l):n(t)}function l(t){return eQ(t)?tm(e,a)(t):a(t)}function a(t){return tp(e,s,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(t)}function s(t){return e.attempt(tv,c,c)(t)}function c(t){return eX(t)?e3(e,u,"whitespace")(t):u(t)}function u(o){return null===o||eJ(o)?(e.exit("definition"),i.parser.defined.push(r),t(o)):n(o)}}}},tZ={[-2]:ty,[-1]:ty,32:ty},t$={35:{name:"headingAtx",resolve:function(e,t){let n,r,i=e.length-2,o=3;return"whitespace"===e[3][1].type&&(o+=2),i-2>o&&"whitespace"===e[i][1].type&&(i-=2),"atxHeadingSequence"===e[i][1].type&&(o===i-1||i-4>o&&"whitespace"===e[i-2][1].type)&&(i-=o+1===i?2:4),i>o&&(n={type:"atxHeadingText",start:e[o][1].start,end:e[i][1].end},r={type:"chunkText",start:e[o][1].start,end:e[i][1].end,contentType:"text"},eR(e,o,i-o+1,[["enter",n,t],["enter",r,t],["exit",r,t],["exit",n,t]])),e},tokenize:function(e,t,n){let r=0;return function(i){return e.enter("atxHeading"),e.enter("atxHeadingSequence"),function i(o){return 35===o&&r++<6?(e.consume(o),i):null===o||eQ(o)?(e.exit("atxHeadingSequence"),function n(r){return 35===r?(e.enter("atxHeadingSequence"),function t(r){return 35===r?(e.consume(r),t):(e.exit("atxHeadingSequence"),n(r))}(r)):null===r||eJ(r)?(e.exit("atxHeading"),t(r)):eX(r)?e3(e,n,"whitespace")(r):(e.enter("atxHeadingText"),function t(r){return null===r||35===r||eQ(r)?(e.exit("atxHeadingText"),n(r)):(e.consume(r),t)}(r))}(o)):n(o)}(i)}}},42:ta,45:[tx,ta],60:{concrete:!0,name:"htmlFlow",resolveTo:function(e){let t=e.length;for(;t--&&("enter"!==e[t][0]||"htmlFlow"!==e[t][1].type););return t>1&&"linePrefix"===e[t-2][1].type&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2)),e},tokenize:function(e,t,n){let r,i,o,l,a;let s=this;return function(t){return e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(t),c};function c(l){return 33===l?(e.consume(l),u):47===l?(e.consume(l),i=!0,h):63===l?(e.consume(l),r=3,s.interrupt?t:M):eU(l)?(e.consume(l),o=String.fromCharCode(l),f):n(l)}function u(i){return 45===i?(e.consume(i),r=2,d):91===i?(e.consume(i),r=5,l=0,p):eU(i)?(e.consume(i),r=4,s.interrupt?t:M):n(i)}function d(r){return 45===r?(e.consume(r),s.interrupt?t:M):n(r)}function p(r){let i="CDATA[";return r===i.charCodeAt(l++)?(e.consume(r),l===i.length)?s.interrupt?t:C:p:n(r)}function h(t){return eU(t)?(e.consume(t),o=String.fromCharCode(t),f):n(t)}function f(l){if(null===l||47===l||62===l||eQ(l)){let a=47===l,c=o.toLowerCase();return!a&&!i&&tw.includes(c)?(r=1,s.interrupt?t(l):C(l)):tk.includes(o.toLowerCase())?(r=6,a)?(e.consume(l),m):s.interrupt?t(l):C(l):(r=7,s.interrupt&&!s.parser.lazy[s.now().line]?n(l):i?function t(n){return eX(n)?(e.consume(n),t):w(n)}(l):g(l))}return 45===l||eZ(l)?(e.consume(l),o+=String.fromCharCode(l),f):n(l)}function m(r){return 62===r?(e.consume(r),s.interrupt?t:C):n(r)}function g(t){return 47===t?(e.consume(t),w):58===t||95===t||eU(t)?(e.consume(t),v):eX(t)?(e.consume(t),g):w(t)}function v(t){return 45===t||46===t||58===t||95===t||eZ(t)?(e.consume(t),v):y(t)}function y(t){return 61===t?(e.consume(t),b):eX(t)?(e.consume(t),y):g(t)}function b(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),a=t,x):eX(t)?(e.consume(t),b):function t(n){return null===n||34===n||39===n||47===n||60===n||61===n||62===n||96===n||eQ(n)?y(n):(e.consume(n),t)}(t)}function x(t){return t===a?(e.consume(t),a=null,k):null===t||eJ(t)?n(t):(e.consume(t),x)}function k(e){return 47===e||62===e||eX(e)?g(e):n(e)}function w(t){return 62===t?(e.consume(t),_):n(t)}function _(t){return null===t||eJ(t)?C(t):eX(t)?(e.consume(t),_):n(t)}function C(t){return 45===t&&2===r?(e.consume(t),D):60===t&&1===r?(e.consume(t),L):62===t&&4===r?(e.consume(t),I):63===t&&3===r?(e.consume(t),M):93===t&&5===r?(e.consume(t),N):eJ(t)&&(6===r||7===r)?(e.exit("htmlFlowData"),e.check(t_,P,S)(t)):null===t||eJ(t)?(e.exit("htmlFlowData"),S(t)):(e.consume(t),C)}function S(t){return e.check(tC,j,P)(t)}function j(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),E}function E(t){return null===t||eJ(t)?S(t):(e.enter("htmlFlowData"),C(t))}function D(t){return 45===t?(e.consume(t),M):C(t)}function L(t){return 47===t?(e.consume(t),o="",T):C(t)}function T(t){if(62===t){let n=o.toLowerCase();return tw.includes(n)?(e.consume(t),I):C(t)}return eU(t)&&o.length<8?(e.consume(t),o+=String.fromCharCode(t),T):C(t)}function N(t){return 93===t?(e.consume(t),M):C(t)}function M(t){return 62===t?(e.consume(t),I):45===t&&2===r?(e.consume(t),M):C(t)}function I(t){return null===t||eJ(t)?(e.exit("htmlFlowData"),P(t)):(e.consume(t),I)}function P(n){return e.exit("htmlFlow"),t(n)}}},61:tx,95:ta,96:tj,126:tj},tG={38:tT,92:tN},tW={[-5]:tM,[-4]:tM,[-3]:tM,33:tR,38:tT,42:tB,60:[{name:"autolink",tokenize:function(e,t,n){let r=0;return function(t){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(t),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),i};function i(t){return eU(t)?(e.consume(t),o):64===t?n(t):a(t)}function o(t){return 43===t||45===t||46===t||eZ(t)?(r=1,function t(n){return 58===n?(e.consume(n),r=0,l):(43===n||45===n||46===n||eZ(n))&&r++<32?(e.consume(n),t):(r=0,a(n))}(t)):a(t)}function l(r){return 62===r?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(r),e.exit("autolinkMarker"),e.exit("autolink"),t):null===r||32===r||60===r||eG(r)?n(r):(e.consume(r),l)}function a(t){return 64===t?(e.consume(t),s):e$(t)?(e.consume(t),a):n(t)}function s(i){return eZ(i)?function i(o){return 46===o?(e.consume(o),r=0,s):62===o?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(o),e.exit("autolinkMarker"),e.exit("autolink"),t):function t(o){if((45===o||eZ(o))&&r++<63){let n=45===o?t:i;return e.consume(o),n}return n(o)}(o)}(i):n(i)}}},{name:"htmlText",tokenize:function(e,t,n){let r,i,o;let l=this;return function(t){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(t),a};function a(t){return 33===t?(e.consume(t),s):47===t?(e.consume(t),x):63===t?(e.consume(t),y):eU(t)?(e.consume(t),w):n(t)}function s(t){return 45===t?(e.consume(t),c):91===t?(e.consume(t),i=0,h):eU(t)?(e.consume(t),v):n(t)}function c(t){return 45===t?(e.consume(t),p):n(t)}function u(t){return null===t?n(t):45===t?(e.consume(t),d):eJ(t)?(o=u,T(t)):(e.consume(t),u)}function d(t){return 45===t?(e.consume(t),p):u(t)}function p(e){return 62===e?L(e):45===e?d(e):u(e)}function h(t){let r="CDATA[";return t===r.charCodeAt(i++)?(e.consume(t),i===r.length?f:h):n(t)}function f(t){return null===t?n(t):93===t?(e.consume(t),m):eJ(t)?(o=f,T(t)):(e.consume(t),f)}function m(t){return 93===t?(e.consume(t),g):f(t)}function g(t){return 62===t?L(t):93===t?(e.consume(t),g):f(t)}function v(t){return null===t||62===t?L(t):eJ(t)?(o=v,T(t)):(e.consume(t),v)}function y(t){return null===t?n(t):63===t?(e.consume(t),b):eJ(t)?(o=y,T(t)):(e.consume(t),y)}function b(e){return 62===e?L(e):y(e)}function x(t){return eU(t)?(e.consume(t),k):n(t)}function k(t){return 45===t||eZ(t)?(e.consume(t),k):function t(n){return eJ(n)?(o=t,T(n)):eX(n)?(e.consume(n),t):L(n)}(t)}function w(t){return 45===t||eZ(t)?(e.consume(t),w):47===t||62===t||eQ(t)?_(t):n(t)}function _(t){return 47===t?(e.consume(t),L):58===t||95===t||eU(t)?(e.consume(t),C):eJ(t)?(o=_,T(t)):eX(t)?(e.consume(t),_):L(t)}function C(t){return 45===t||46===t||58===t||95===t||eZ(t)?(e.consume(t),C):function t(n){return 61===n?(e.consume(n),S):eJ(n)?(o=t,T(n)):eX(n)?(e.consume(n),t):_(n)}(t)}function S(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),r=t,j):eJ(t)?(o=S,T(t)):eX(t)?(e.consume(t),S):(e.consume(t),E)}function j(t){return t===r?(e.consume(t),r=void 0,D):null===t?n(t):eJ(t)?(o=j,T(t)):(e.consume(t),j)}function E(t){return null===t||34===t||39===t||60===t||61===t||96===t?n(t):47===t||62===t||eQ(t)?_(t):(e.consume(t),E)}function D(e){return 47===e||62===e||eQ(e)?_(e):n(e)}function L(r){return 62===r?(e.consume(r),e.exit("htmlTextData"),e.exit("htmlText"),t):n(r)}function T(t){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),N}function N(t){return eX(t)?e3(e,M,"linePrefix",l.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):M(t)}function M(t){return e.enter("htmlTextData"),o(t)}}}],91:tH,92:[{name:"hardBreakEscape",tokenize:function(e,t,n){return function(t){return e.enter("hardBreakEscape"),e.consume(t),r};function r(r){return eJ(r)?(e.exit("hardBreakEscape"),t(r)):n(r)}}},tN],93:tP,95:tB,96:{name:"codeText",previous:function(e){return 96!==e||"characterEscape"===this.events[this.events.length-1][1].type},resolve:function(e){let t,n,r=e.length-4,i=3;if(("lineEnding"===e[3][1].type||"space"===e[i][1].type)&&("lineEnding"===e[r][1].type||"space"===e[r][1].type)){for(t=i;++t<r;)if("codeTextData"===e[t][1].type){e[i][1].type="codeTextPadding",e[r][1].type="codeTextPadding",i+=2,r-=2;break}}for(t=i-1,r++;++t<=r;)void 0===n?t!==r&&"lineEnding"!==e[t][1].type&&(n=t):(t===r||"lineEnding"===e[t][1].type)&&(e[n][1].type="codeTextData",t!==n+2&&(e[n][1].end=e[t-1][1].end,e.splice(n+2,t-n-2),r-=t-n-2,t=n+2),n=void 0);return e},tokenize:function(e,t,n){let r,i,o=0;return function(t){return e.enter("codeText"),e.enter("codeTextSequence"),function t(n){return 96===n?(e.consume(n),o++,t):(e.exit("codeTextSequence"),l(n))}(t)};function l(s){return null===s?n(s):32===s?(e.enter("space"),e.consume(s),e.exit("space"),l):96===s?(i=e.enter("codeTextSequence"),r=0,function n(l){return 96===l?(e.consume(l),r++,n):r===o?(e.exit("codeTextSequence"),e.exit("codeText"),t(l)):(i.type="codeTextData",a(l))}(s)):eJ(s)?(e.enter("lineEnding"),e.consume(s),e.exit("lineEnding"),l):(e.enter("codeTextData"),a(s))}function a(t){return null===t||32===t||96===t||eJ(t)?(e.exit("codeTextData"),l(t)):(e.consume(t),a)}}}},tK={null:[tB,tt]},tY={null:[42,95]},tJ={null:[]},tQ=/[\0\t\n\r]/g;function tX(e,t){let n=Number.parseInt(e,t);return n<9||11===n||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||(65535&n)==65535||(65535&n)==65534||n>1114111?"�":String.fromCodePoint(n)}let t1=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function t0(e,t,n){if(t)return t;if(35===n.charCodeAt(0)){let e=n.charCodeAt(1),t=120===e||88===e;return tX(n.slice(t?2:1),t?16:10)}return tL(n)||e}let t2={}.hasOwnProperty;function t3(e){return{line:e.line,column:e.column,offset:e.offset}}function t4(e,t){if(e)throw Error("Cannot close `"+e.type+"` ("+ef({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+ef({start:t.start,end:t.end})+") is open");throw Error("Cannot close document, a token (`"+t.type+"`, "+ef({start:t.start,end:t.end})+") is still open")}function t5(e){let t=this;t.parser=function(n){var r,o;let l,a,s,c;return"string"!=typeof(r={...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})&&(o=r,r=void 0),(function(e){let t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:r(v),autolinkProtocol:c,autolinkEmail:c,atxHeading:r(f),blockQuote:r(function(){return{type:"blockquote",children:[]}}),characterEscape:c,characterReference:c,codeFenced:r(h),codeFencedFenceInfo:i,codeFencedFenceMeta:i,codeIndented:r(h,i),codeText:r(function(){return{type:"inlineCode",value:""}},i),codeTextData:c,data:c,codeFlowValue:c,definition:r(function(){return{type:"definition",identifier:"",label:null,title:null,url:""}}),definitionDestinationString:i,definitionLabelString:i,definitionTitleString:i,emphasis:r(function(){return{type:"emphasis",children:[]}}),hardBreakEscape:r(m),hardBreakTrailing:r(m),htmlFlow:r(g,i),htmlFlowData:c,htmlText:r(g,i),htmlTextData:c,image:r(function(){return{type:"image",title:null,url:"",alt:null}}),label:i,link:r(v),listItem:r(function(e){return{type:"listItem",spread:e._spread,checked:null,children:[]}}),listItemValue:function(e){this.data.expectingFirstListItemValue&&(this.stack[this.stack.length-2].start=Number.parseInt(this.sliceSerialize(e),10),this.data.expectingFirstListItemValue=void 0)},listOrdered:r(y,function(){this.data.expectingFirstListItemValue=!0}),listUnordered:r(y),paragraph:r(function(){return{type:"paragraph",children:[]}}),reference:function(){this.data.referenceType="collapsed"},referenceString:i,resourceDestinationString:i,resourceTitleString:i,setextHeading:r(f),strong:r(function(){return{type:"strong",children:[]}}),thematicBreak:r(function(){return{type:"thematicBreak"}})},exit:{atxHeading:l(),atxHeadingSequence:function(e){let t=this.stack[this.stack.length-1];if(!t.depth){let n=this.sliceSerialize(e).length;t.depth=n}},autolink:l(),autolinkEmail:function(e){u.call(this,e),this.stack[this.stack.length-1].url="mailto:"+this.sliceSerialize(e)},autolinkProtocol:function(e){u.call(this,e),this.stack[this.stack.length-1].url=this.sliceSerialize(e)},blockQuote:l(),characterEscapeValue:u,characterReferenceMarkerHexadecimal:p,characterReferenceMarkerNumeric:p,characterReferenceValue:function(e){let t;let n=this.sliceSerialize(e),r=this.data.characterReferenceType;r?(t=tX(n,"characterReferenceMarkerNumeric"===r?10:16),this.data.characterReferenceType=void 0):t=tL(n);let i=this.stack[this.stack.length-1];i.value+=t},characterReference:function(e){this.stack.pop().position.end=t3(e.end)},codeFenced:l(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}),codeFencedFence:function(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)},codeFencedFenceInfo:function(){let e=this.resume();this.stack[this.stack.length-1].lang=e},codeFencedFenceMeta:function(){let e=this.resume();this.stack[this.stack.length-1].meta=e},codeFlowValue:u,codeIndented:l(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/(\r?\n|\r)$/g,"")}),codeText:l(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),codeTextData:u,data:u,definition:l(),definitionDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},definitionLabelString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=tg(this.sliceSerialize(e)).toLowerCase()},definitionTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},emphasis:l(),hardBreakEscape:l(d),hardBreakTrailing:l(d),htmlFlow:l(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlFlowData:u,htmlText:l(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlTextData:u,image:l(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),label:function(){let e=this.stack[this.stack.length-1],t=this.resume(),n=this.stack[this.stack.length-1];if(this.data.inReference=!0,"link"===n.type){let t=e.children;n.children=t}else n.alt=t},labelText:function(e){let t=this.sliceSerialize(e),n=this.stack[this.stack.length-2];n.label=t.replace(t1,t0),n.identifier=tg(t).toLowerCase()},lineEnding:function(e){let n=this.stack[this.stack.length-1];if(this.data.atHardBreak){n.children[n.children.length-1].position.end=t3(e.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(n.type)&&(c.call(this,e),u.call(this,e))},link:l(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),listItem:l(),listOrdered:l(),listUnordered:l(),paragraph:l(),referenceString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=tg(this.sliceSerialize(e)).toLowerCase(),this.data.referenceType="full"},resourceDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},resourceTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},resource:function(){this.data.inReference=void 0},setextHeading:l(function(){this.data.setextHeadingSlurpLineEnding=void 0}),setextHeadingLineSequence:function(e){this.stack[this.stack.length-1].depth=61===this.sliceSerialize(e).codePointAt(0)?1:2},setextHeadingText:function(){this.data.setextHeadingSlurpLineEnding=!0},strong:l(),thematicBreak:l()}};(function e(t,n){let r=-1;for(;++r<n.length;){let i=n[r];Array.isArray(i)?e(t,i):function(e,t){let n;for(n in t)if(t2.call(t,n))switch(n){case"canContainEols":{let r=t[n];r&&e[n].push(...r);break}case"transforms":{let r=t[n];r&&e[n].push(...r);break}case"enter":case"exit":{let r=t[n];r&&Object.assign(e[n],r)}}}(t,i)}})(t,(e||{}).mdastExtensions||[]);let n={};return function(e){let r={type:"root",children:[]},l={stack:[r],tokenStack:[],config:t,enter:o,exit:a,buffer:i,resume:s,data:n},c=[],u=-1;for(;++u<e.length;)("listOrdered"===e[u][1].type||"listUnordered"===e[u][1].type)&&("enter"===e[u][0]?c.push(u):u=function(e,t,n){let r,i,o,l,a=t-1,s=-1,c=!1;for(;++a<=n;){let t=e[a];switch(t[1].type){case"listUnordered":case"listOrdered":case"blockQuote":"enter"===t[0]?s++:s--,l=void 0;break;case"lineEndingBlank":"enter"===t[0]&&(!r||l||s||o||(o=a),l=void 0);break;case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:l=void 0}if(!s&&"enter"===t[0]&&"listItemPrefix"===t[1].type||-1===s&&"exit"===t[0]&&("listUnordered"===t[1].type||"listOrdered"===t[1].type)){if(r){let l=a;for(i=void 0;l--;){let t=e[l];if("lineEnding"===t[1].type||"lineEndingBlank"===t[1].type){if("exit"===t[0])continue;i&&(e[i][1].type="lineEndingBlank",c=!0),t[1].type="lineEnding",i=l}else if("linePrefix"===t[1].type||"blockQuotePrefix"===t[1].type||"blockQuotePrefixWhitespace"===t[1].type||"blockQuoteMarker"===t[1].type||"listItemIndent"===t[1].type);else break}o&&(!i||o<i)&&(r._spread=!0),r.end=Object.assign({},i?e[i][1].start:t[1].end),e.splice(i||a,0,["exit",r,t[2]]),a++,n++}if("listItemPrefix"===t[1].type){let i={type:"listItem",_spread:!1,start:Object.assign({},t[1].start),end:void 0};r=i,e.splice(a,0,["enter",i,t[2]]),a++,n++,o=void 0,l=!0}}}return e[t][1]._spread=c,n}(e,c.pop(),u));for(u=-1;++u<e.length;){let n=t[e[u][0]];t2.call(n,e[u][1].type)&&n[e[u][1].type].call(Object.assign({sliceSerialize:e[u][2].sliceSerialize},l),e[u][1])}if(l.tokenStack.length>0){let e=l.tokenStack[l.tokenStack.length-1];(e[1]||t4).call(l,void 0,e[0])}for(r.position={start:t3(e.length>0?e[0][1].start:{line:1,column:1,offset:0}),end:t3(e.length>0?e[e.length-2][1].end:{line:1,column:1,offset:0})},u=-1;++u<t.transforms.length;)r=t.transforms[u](r)||r;return r};function r(e,t){return function(n){o.call(this,e(n),n),t&&t.call(this,n)}}function i(){this.stack.push({type:"fragment",children:[]})}function o(e,t,n){this.stack[this.stack.length-1].children.push(e),this.stack.push(e),this.tokenStack.push([t,n||void 0]),e.position={start:t3(t.start),end:void 0}}function l(e){return function(t){e&&e.call(this,t),a.call(this,t)}}function a(e,t){let n=this.stack.pop(),r=this.tokenStack.pop();if(r)r[0].type!==e.type&&(t?t.call(this,e,r[0]):(r[1]||t4).call(this,e,r[0]));else throw Error("Cannot close `"+e.type+"` ("+ef({start:e.start,end:e.end})+"): it’s not open");n.position.end=t3(e.end)}function s(){return eA(this.stack.pop(),"boolean"!=typeof ez.includeImageAlt||ez.includeImageAlt,"boolean"!=typeof ez.includeHtml||ez.includeHtml)}function c(e){let t=this.stack[this.stack.length-1].children,n=t[t.length-1];n&&"text"===n.type||((n={type:"text",value:""}).position={start:t3(e.start),end:void 0},t.push(n)),this.stack.push(n)}function u(e){let t=this.stack.pop();t.value+=this.sliceSerialize(e),t.position.end=t3(e.end)}function d(){this.data.atHardBreak=!0}function p(e){this.data.characterReferenceType=e.type}function h(){return{type:"code",lang:null,meta:null,value:""}}function f(){return{type:"heading",depth:0,children:[]}}function m(){return{type:"break"}}function g(){return{type:"html",value:""}}function v(){return{type:"link",title:null,url:"",children:[]}}function y(e){return{type:"list",ordered:"listOrdered"===e.type,start:null,spread:e._spread,children:[]}}})(o)(function(e){for(;!eH(e););return e}((function(e){let t={constructs:function(e){let t={},n=-1;for(;++n<e.length;)(function(e,t){let n;for(n in t){let r;let i=(eV.call(e,n)?e[n]:void 0)||(e[n]={}),o=t[n];if(o)for(r in o){eV.call(i,r)||(i[r]=[]);let e=o[r];(function(e,t){let n=-1,r=[];for(;++n<t.length;)("after"===t[n].add?e:r).push(t[n]);eR(e,0,0,r)})(i[r],Array.isArray(e)?e:e?[e]:[])}}})(t,e[n]);return t}([i,...(e||{}).extensions||[]]),content:n(e4),defined:[],document:n(e5),flow:n(te),lazy:{},string:n(tn),text:n(tr)};return t;function n(e){return function(n){return function(e,t,n){let r={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0},i={},o=[],l=[],a=[],s={attempt:f(function(e,t){m(e,t.from)}),check:f(h),consume:function(e){eJ(e)?(r.line++,r.column=1,r.offset+=-3===e?2:1,g()):-1!==e&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===l[r._index].length&&(r._bufferIndex=-1,r._index++)),c.previous=e},enter:function(e,t){let n=t||{};return n.type=e,n.start=p(),c.events.push(["enter",n,c]),a.push(n),n},exit:function(e){let t=a.pop();return t.end=p(),c.events.push(["exit",t,c]),t},interrupt:f(h,{interrupt:!0})},c={code:null,containerState:{},defineSkip:function(e){i[e.line]=e.column,g()},events:[],now:p,parser:e,previous:null,sliceSerialize:function(e,t){return function(e,t){let n,r=-1,i=[];for(;++r<e.length;){let o;let l=e[r];if("string"==typeof l)o=l;else switch(l){case -5:o="\r";break;case -4:o="\n";break;case -3:o="\r\n";break;case -2:o=t?" ":"	";break;case -1:if(!t&&n)continue;o=" ";break;default:o=String.fromCharCode(l)}n=-2===l,i.push(o)}return i.join("")}(d(e),t)},sliceStream:d,write:function(e){return(l=eO(l,e),function(){let e;for(;r._index<l.length;){let n=l[r._index];if("string"==typeof n)for(e=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===e&&r._bufferIndex<n.length;){var t;t=n.charCodeAt(r._bufferIndex),u=u(t)}else u=u(n)}}(),null!==l[l.length-1])?[]:(m(t,0),c.events=tI(o,c.events,c),c.events)}},u=t.tokenize.call(c,s);return t.resolveAll&&o.push(t),c;function d(e){return function(e,t){let n;let r=t.start._index,i=t.start._bufferIndex,o=t.end._index,l=t.end._bufferIndex;if(r===o)n=[e[r].slice(i,l)];else{if(n=e.slice(r,o),i>-1){let e=n[0];"string"==typeof e?n[0]=e.slice(i):n.shift()}l>0&&n.push(e[o].slice(0,l))}return n}(l,e)}function p(){let{_bufferIndex:e,_index:t,line:n,column:i,offset:o}=r;return{_bufferIndex:e,_index:t,line:n,column:i,offset:o}}function h(e,t){t.restore()}function f(e,t){return function(n,i,o){let l,u,d,h;return Array.isArray(n)?f(n):"tokenize"in n?f([n]):function(e){let t=null!==e&&n[e],r=null!==e&&n.null;return f([...Array.isArray(t)?t:t?[t]:[],...Array.isArray(r)?r:r?[r]:[]])(e)};function f(e){return(l=e,u=0,0===e.length)?o:m(e[u])}function m(e){return function(n){return(h=function(){let e=p(),t=c.previous,n=c.currentConstruct,i=c.events.length,o=Array.from(a);return{from:i,restore:function(){r=e,c.previous=t,c.currentConstruct=n,c.events.length=i,a=o,g()}}}(),d=e,e.partial||(c.currentConstruct=e),e.name&&c.parser.constructs.disable.null.includes(e.name))?y(n):e.tokenize.call(t?Object.assign(Object.create(c),t):c,s,v,y)(n)}}function v(t){return e(d,h),i}function y(e){return(h.restore(),++u<l.length)?m(l[u]):o}}}function m(e,t){e.resolveAll&&!o.includes(e)&&o.push(e),e.resolve&&eR(c.events,t,c.events.length-t,e.resolve(c.events.slice(t),c)),e.resolveTo&&(c.events=e.resolveTo(c.events,c))}function g(){r.line in i&&r.column<2&&(r.column=i[r.line],r.offset+=i[r.line]-1)}}(t,e,n)}}})(o).document().write((a=1,s="",c=!0,function(e,t,n){let r,i,o,u,d;let p=[];for(e=s+("string"==typeof e?e.toString():new TextDecoder(t||void 0).decode(e)),o=0,s="",c&&(65279===e.charCodeAt(0)&&o++,c=void 0);o<e.length;){if(tQ.lastIndex=o,u=(r=tQ.exec(e))&&void 0!==r.index?r.index:e.length,d=e.charCodeAt(u),!r){s=e.slice(o);break}if(10===d&&o===u&&l)p.push(-3),l=void 0;else switch(l&&(p.push(-5),l=void 0),o<u&&(p.push(e.slice(o,u)),a+=u-o),d){case 0:p.push(65533),a++;break;case 9:for(i=4*Math.ceil(a/4),p.push(-2);a++<i;)p.push(-1);break;case 10:p.push(-4),a=1;break;default:l=!0,a=1}o=u+1}return n&&(l&&p.push(-5),s&&p.push(s),p.push(null)),p})(n,r,!0))))}}let t6="object"==typeof self?self:globalThis,t7=(e,t)=>{let n=(t,n)=>(e.set(n,t),t),r=i=>{if(e.has(i))return e.get(i);let[o,l]=t[i];switch(o){case 0:case -1:return n(l,i);case 1:{let e=n([],i);for(let t of l)e.push(r(t));return e}case 2:{let e=n({},i);for(let[t,n]of l)e[r(t)]=r(n);return e}case 3:return n(new Date(l),i);case 4:{let{source:e,flags:t}=l;return n(new RegExp(e,t),i)}case 5:{let e=n(new Map,i);for(let[t,n]of l)e.set(r(t),r(n));return e}case 6:{let e=n(new Set,i);for(let t of l)e.add(r(t));return e}case 7:{let{name:e,message:t}=l;return n(new t6[e](t),i)}case 8:return n(BigInt(l),i);case"BigInt":return n(Object(BigInt(l)),i)}return n(new t6[o](l),i)};return r},t9=e=>t7(new Map,e)(0),{toString:t8}={},{keys:ne}=Object,nt=e=>{let t=typeof e;if("object"!==t||!e)return[0,t];let n=t8.call(e).slice(8,-1);switch(n){case"Array":return[1,""];case"Object":return[2,""];case"Date":return[3,""];case"RegExp":return[4,""];case"Map":return[5,""];case"Set":return[6,""]}return n.includes("Array")?[1,n]:n.includes("Error")?[7,n]:[2,n]},nn=([e,t])=>0===e&&("function"===t||"symbol"===t),nr=(e,t,n,r)=>{let i=(e,t)=>{let i=r.push(e)-1;return n.set(t,i),i},o=r=>{if(n.has(r))return n.get(r);let[l,a]=nt(r);switch(l){case 0:{let t=r;switch(a){case"bigint":l=8,t=r.toString();break;case"function":case"symbol":if(e)throw TypeError("unable to serialize "+a);t=null;break;case"undefined":return i([-1],r)}return i([l,t],r)}case 1:{if(a)return i([a,[...r]],r);let e=[],t=i([l,e],r);for(let t of r)e.push(o(t));return t}case 2:{if(a)switch(a){case"BigInt":return i([a,r.toString()],r);case"Boolean":case"Number":case"String":return i([a,r.valueOf()],r)}if(t&&"toJSON"in r)return o(r.toJSON());let n=[],s=i([l,n],r);for(let t of ne(r))(e||!nn(nt(r[t])))&&n.push([o(t),o(r[t])]);return s}case 3:return i([l,r.toISOString()],r);case 4:{let{source:e,flags:t}=r;return i([l,{source:e,flags:t}],r)}case 5:{let t=[],n=i([l,t],r);for(let[n,i]of r)(e||!(nn(nt(n))||nn(nt(i))))&&t.push([o(n),o(i)]);return n}case 6:{let t=[],n=i([l,t],r);for(let n of r)(e||!nn(nt(n)))&&t.push(o(n));return n}}let{message:s}=r;return i([l,{name:a,message:s}],r)};return o},ni=(e,{json:t,lossy:n}={})=>{let r=[];return nr(!(t||n),!!t,new Map,r)(e),r},no="function"==typeof structuredClone?(e,t)=>t&&("json"in t||"lossy"in t)?t9(ni(e,t)):structuredClone(e):(e,t)=>t9(ni(e,t));function nl(e){let t=[],n=-1,r=0,i=0;for(;++n<e.length;){let o=e.charCodeAt(n),l="";if(37===o&&eZ(e.charCodeAt(n+1))&&eZ(e.charCodeAt(n+2)))i=2;else if(o<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(o))||(l=String.fromCharCode(o));else if(o>55295&&o<57344){let t=e.charCodeAt(n+1);o<56320&&t>56319&&t<57344?(l=String.fromCharCode(o,t),i=1):l="�"}else l=String.fromCharCode(o);l&&(t.push(e.slice(r,n),encodeURIComponent(l)),r=n+i+1,l=""),i&&(n+=i,i=0)}return t.join("")+e.slice(r)}function na(e,t){let n=[{type:"text",value:"↩"}];return t>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),n}function ns(e,t){return"Back to reference "+(e+1)+(t>1?"-"+t:"")}let nc=function(e){if(null==e)return nd;if("function"==typeof e)return nu(e);if("object"==typeof e)return Array.isArray(e)?function(e){let t=[],n=-1;for(;++n<e.length;)t[n]=nc(e[n]);return nu(function(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1})}(e):nu(function(t){let n;for(n in e)if(t[n]!==e[n])return!1;return!0});if("string"==typeof e)return nu(function(t){return t&&t.type===e});throw Error("Expected function, string, or object as test")};function nu(e){return function(t,n,r){var i;return!!(null!==(i=t)&&"object"==typeof i&&"type"in i&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function nd(){return!0}let np=[];function nh(e,t,n,r){let i,o,l;"function"==typeof t&&"function"!=typeof n?(o=void 0,l=t,i=n):(o=t,l=n,i=r),function(e,t,n,r){let i;"function"==typeof t&&"function"!=typeof n?(r=n,n=t):i=t;let o=nc(i),l=r?-1:1;(function e(i,a,s){let c=i&&"object"==typeof i?i:{};if("string"==typeof c.type){let e="string"==typeof c.tagName?c.tagName:"string"==typeof c.name?c.name:void 0;Object.defineProperty(u,"name",{value:"node (\x1b[33m"+i.type+(e?"<"+e+">":"")+"\x1b[39m)"})}return u;function u(){var c;let u,d,p,h=np;if((!t||o(i,a,s[s.length-1]||void 0))&&!1===(h=Array.isArray(c=n(i,s))?c:"number"==typeof c?[!0,c]:null==c?np:[c])[0])return h;if("children"in i&&i.children&&i.children&&"skip"!==h[0])for(d=(r?i.children.length:-1)+l,p=s.concat(i);d>-1&&d<i.children.length;){if(!1===(u=e(i.children[d],d,p)())[0])return u;d="number"==typeof u[1]?u[1]:d+l}return h}})(e,void 0,[])()}(e,o,function(e,t){let n=t[t.length-1],r=n?n.children.indexOf(e):void 0;return l(e,r,n)},i)}function nf(e,t){let n=t.referenceType,r="]";if("collapsed"===n?r+="[]":"full"===n&&(r+="["+(t.label||t.identifier)+"]"),"imageReference"===t.type)return[{type:"text",value:"!["+t.alt+r}];let i=e.all(t),o=i[0];o&&"text"===o.type?o.value="["+o.value:i.unshift({type:"text",value:"["});let l=i[i.length-1];return l&&"text"===l.type?l.value+=r:i.push({type:"text",value:r}),i}function nm(e){let t=e.spread;return null==t?e.children.length>1:t}function ng(e,t,n){let r=0,i=e.length;if(t){let t=e.codePointAt(r);for(;9===t||32===t;)r++,t=e.codePointAt(r)}if(n){let t=e.codePointAt(i-1);for(;9===t||32===t;)i--,t=e.codePointAt(i-1)}return i>r?e.slice(r,i):""}let nv={blockquote:function(e,t){let n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)},break:function(e,t){let n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:"\n"}]},code:function(e,t){let n=t.value?t.value+"\n":"",r={};t.lang&&(r.className=["language-"+t.lang]);let i={type:"element",tagName:"code",properties:r,children:[{type:"text",value:n}]};return t.meta&&(i.data={meta:t.meta}),e.patch(t,i),i={type:"element",tagName:"pre",properties:{},children:[i=e.applyData(t,i)]},e.patch(t,i),i},delete:function(e,t){let n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},emphasis:function(e,t){let n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},footnoteReference:function(e,t){let n;let r="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",i=String(t.identifier).toUpperCase(),o=nl(i.toLowerCase()),l=e.footnoteOrder.indexOf(i),a=e.footnoteCounts.get(i);void 0===a?(a=0,e.footnoteOrder.push(i),n=e.footnoteOrder.length):n=l+1,a+=1,e.footnoteCounts.set(i,a);let s={type:"element",tagName:"a",properties:{href:"#"+r+"fn-"+o,id:r+"fnref-"+o+(a>1?"-"+a:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(n)}]};e.patch(t,s);let c={type:"element",tagName:"sup",properties:{},children:[s]};return e.patch(t,c),e.applyData(t,c)},heading:function(e,t){let n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},html:function(e,t){if(e.options.allowDangerousHtml){let n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}},imageReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return nf(e,t);let i={src:nl(r.url||""),alt:t.alt};null!==r.title&&void 0!==r.title&&(i.title=r.title);let o={type:"element",tagName:"img",properties:i,children:[]};return e.patch(t,o),e.applyData(t,o)},image:function(e,t){let n={src:nl(t.url)};null!==t.alt&&void 0!==t.alt&&(n.alt=t.alt),null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,r),e.applyData(t,r)},inlineCode:function(e,t){let n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);let r={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,r),e.applyData(t,r)},linkReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return nf(e,t);let i={href:nl(r.url||"")};null!==r.title&&void 0!==r.title&&(i.title=r.title);let o={type:"element",tagName:"a",properties:i,children:e.all(t)};return e.patch(t,o),e.applyData(t,o)},link:function(e,t){let n={href:nl(t.url)};null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,r),e.applyData(t,r)},listItem:function(e,t,n){let r=e.all(t),i=n?function(e){let t=!1;if("list"===e.type){t=e.spread||!1;let n=e.children,r=-1;for(;!t&&++r<n.length;)t=nm(n[r])}return t}(n):nm(t),o={},l=[];if("boolean"==typeof t.checked){let e;let n=r[0];n&&"element"===n.type&&"p"===n.tagName?e=n:(e={type:"element",tagName:"p",properties:{},children:[]},r.unshift(e)),e.children.length>0&&e.children.unshift({type:"text",value:" "}),e.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),o.className=["task-list-item"]}let a=-1;for(;++a<r.length;){let e=r[a];(i||0!==a||"element"!==e.type||"p"!==e.tagName)&&l.push({type:"text",value:"\n"}),"element"!==e.type||"p"!==e.tagName||i?l.push(e):l.push(...e.children)}let s=r[r.length-1];s&&(i||"element"!==s.type||"p"!==s.tagName)&&l.push({type:"text",value:"\n"});let c={type:"element",tagName:"li",properties:o,children:l};return e.patch(t,c),e.applyData(t,c)},list:function(e,t){let n={},r=e.all(t),i=-1;for("number"==typeof t.start&&1!==t.start&&(n.start=t.start);++i<r.length;){let e=r[i];if("element"===e.type&&"li"===e.tagName&&e.properties&&Array.isArray(e.properties.className)&&e.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}let o={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(r,!0)};return e.patch(t,o),e.applyData(t,o)},paragraph:function(e,t){let n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},root:function(e,t){let n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)},strong:function(e,t){let n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},table:function(e,t){let n=e.all(t),r=n.shift(),i=[];if(r){let n={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(t.children[0],n),i.push(n)}if(n.length>0){let r={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},o=ep(t.children[1]),l=ed(t.children[t.children.length-1]);o&&l&&(r.position={start:o,end:l}),i.push(r)}let o={type:"element",tagName:"table",properties:{},children:e.wrap(i,!0)};return e.patch(t,o),e.applyData(t,o)},tableCell:function(e,t){let n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},tableRow:function(e,t,n){let r=n?n.children:void 0,i=0===(r?r.indexOf(t):1)?"th":"td",o=n&&"table"===n.type?n.align:void 0,l=o?o.length:t.children.length,a=-1,s=[];for(;++a<l;){let n=t.children[a],r={},l=o?o[a]:void 0;l&&(r.align=l);let c={type:"element",tagName:i,properties:r,children:[]};n&&(c.children=e.all(n),e.patch(n,c),c=e.applyData(n,c)),s.push(c)}let c={type:"element",tagName:"tr",properties:{},children:e.wrap(s,!0)};return e.patch(t,c),e.applyData(t,c)},text:function(e,t){let n={type:"text",value:function(e){let t=String(e),n=/\r?\n|\r/g,r=n.exec(t),i=0,o=[];for(;r;)o.push(ng(t.slice(i,r.index),i>0,!0),r[0]),i=r.index+r[0].length,r=n.exec(t);return o.push(ng(t.slice(i),i>0,!1)),o.join("")}(String(t.value))};return e.patch(t,n),e.applyData(t,n)},thematicBreak:function(e,t){let n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)},toml:ny,yaml:ny,definition:ny,footnoteDefinition:ny};function ny(){}let nb={}.hasOwnProperty,nx={};function nk(e,t){e.position&&(t.position=function(e){let t=ep(e),n=ed(e);if(t&&n)return{start:t,end:n}}(e))}function nw(e,t){let n=t;if(e&&e.data){let t=e.data.hName,r=e.data.hChildren,i=e.data.hProperties;"string"==typeof t&&("element"===n.type?n.tagName=t:n={type:"element",tagName:t,properties:{},children:"children"in n?n.children:[n]}),"element"===n.type&&i&&Object.assign(n.properties,no(i)),"children"in n&&n.children&&null!=r&&(n.children=r)}return n}function n_(e,t){let n=[],r=-1;for(t&&n.push({type:"text",value:"\n"});++r<e.length;)r&&n.push({type:"text",value:"\n"}),n.push(e[r]);return t&&e.length>0&&n.push({type:"text",value:"\n"}),n}function nC(e){let t=0,n=e.charCodeAt(t);for(;9===n||32===n;)t++,n=e.charCodeAt(t);return e.slice(t)}function nS(e,t){let n=function(e,t){let n=t||nx,r=new Map,i=new Map,o={all:function(e){let t=[];if("children"in e){let n=e.children,r=-1;for(;++r<n.length;){let i=o.one(n[r],e);if(i){if(r&&"break"===n[r-1].type&&(Array.isArray(i)||"text"!==i.type||(i.value=nC(i.value)),!Array.isArray(i)&&"element"===i.type)){let e=i.children[0];e&&"text"===e.type&&(e.value=nC(e.value))}Array.isArray(i)?t.push(...i):t.push(i)}}}return t},applyData:nw,definitionById:r,footnoteById:i,footnoteCounts:new Map,footnoteOrder:[],handlers:{...nv,...n.handlers},one:function(e,t){let n=e.type,r=o.handlers[n];if(nb.call(o.handlers,n)&&r)return r(o,e,t);if(o.options.passThrough&&o.options.passThrough.includes(n)){if("children"in e){let{children:t,...n}=e,r=no(n);return r.children=o.all(e),r}return no(e)}return(o.options.unknownHandler||function(e,t){let n=t.data||{},r="value"in t&&!(nb.call(n,"hProperties")||nb.call(n,"hChildren"))?{type:"text",value:t.value}:{type:"element",tagName:"div",properties:{},children:e.all(t)};return e.patch(t,r),e.applyData(t,r)})(o,e,t)},options:n,patch:nk,wrap:n_};return nh(e,function(e){if("definition"===e.type||"footnoteDefinition"===e.type){let t="definition"===e.type?r:i,n=String(e.identifier).toUpperCase();t.has(n)||t.set(n,e)}}),o}(e,t),r=n.one(e,void 0),i=function(e){let t="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",n=e.options.footnoteBackContent||na,r=e.options.footnoteBackLabel||ns,i=e.options.footnoteLabel||"Footnotes",o=e.options.footnoteLabelTagName||"h2",l=e.options.footnoteLabelProperties||{className:["sr-only"]},a=[],s=-1;for(;++s<e.footnoteOrder.length;){let i=e.footnoteById.get(e.footnoteOrder[s]);if(!i)continue;let o=e.all(i),l=String(i.identifier).toUpperCase(),c=nl(l.toLowerCase()),u=0,d=[],p=e.footnoteCounts.get(l);for(;void 0!==p&&++u<=p;){d.length>0&&d.push({type:"text",value:" "});let e="string"==typeof n?n:n(s,u);"string"==typeof e&&(e={type:"text",value:e}),d.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+c+(u>1?"-"+u:""),dataFootnoteBackref:"",ariaLabel:"string"==typeof r?r:r(s,u),className:["data-footnote-backref"]},children:Array.isArray(e)?e:[e]})}let h=o[o.length-1];if(h&&"element"===h.type&&"p"===h.tagName){let e=h.children[h.children.length-1];e&&"text"===e.type?e.value+=" ":h.children.push({type:"text",value:" "}),h.children.push(...d)}else o.push(...d);let f={type:"element",tagName:"li",properties:{id:t+"fn-"+c},children:e.wrap(o,!0)};e.patch(i,f),a.push(f)}if(0!==a.length)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:o,properties:{...no(l),id:"footnote-label"},children:[{type:"text",value:i}]},{type:"text",value:"\n"},{type:"element",tagName:"ol",properties:{},children:e.wrap(a,!0)},{type:"text",value:"\n"}]}}(n),o=Array.isArray(r)?{type:"root",children:r}:r||{type:"root",children:[]};return i&&o.children.push({type:"text",value:"\n"},i),o}function nj(e,t){return e&&"run"in e?async function(n,r){let i=nS(n,{file:r,...t});await e.run(i,r)}:function(n,r){return nS(n,{file:r,...e||t})}}function nE(e){if(e)throw e}var nD=n(71771);function nL(e){if("object"!=typeof e||null===e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}let nT=require("node:path"),nN=require("node:process");function nM(e){return!!(null!==e&&"object"==typeof e&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&void 0===e.auth)}let nI=require("node:url"),nP=["history","path","basename","stem","extname","dirname"];class nz{constructor(e){let t,n;t=e?nM(e)?{path:e}:"string"==typeof e||function(e){return!!(e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e)}(e)?{value:e}:e:{},this.cwd="cwd"in t?"":nN.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let r=-1;for(;++r<nP.length;){let e=nP[r];e in t&&void 0!==t[e]&&null!==t[e]&&(this[e]="history"===e?[...t[e]]:t[e])}for(n in t)nP.includes(n)||(this[n]=t[n])}get basename(){return"string"==typeof this.path?nT.basename(this.path):void 0}set basename(e){nq(e,"basename"),nA(e,"basename"),this.path=nT.join(this.dirname||"",e)}get dirname(){return"string"==typeof this.path?nT.dirname(this.path):void 0}set dirname(e){nR(this.basename,"dirname"),this.path=nT.join(e||"",this.basename)}get extname(){return"string"==typeof this.path?nT.extname(this.path):void 0}set extname(e){if(nA(e,"extname"),nR(this.dirname,"extname"),e){if(46!==e.codePointAt(0))throw Error("`extname` must start with `.`");if(e.includes(".",1))throw Error("`extname` cannot contain multiple dots")}this.path=nT.join(this.dirname,this.stem+(e||""))}get path(){return this.history[this.history.length-1]}set path(e){nM(e)&&(e=(0,nI.fileURLToPath)(e)),nq(e,"path"),this.path!==e&&this.history.push(e)}get stem(){return"string"==typeof this.path?nT.basename(this.path,this.extname):void 0}set stem(e){nq(e,"stem"),nA(e,"stem"),this.path=nT.join(this.dirname||"",e+(this.extname||""))}fail(e,t,n){let r=this.message(e,t,n);throw r.fatal=!0,r}info(e,t,n){let r=this.message(e,t,n);return r.fatal=void 0,r}message(e,t,n){let r=new ey(e,t,n);return this.path&&(r.name=this.path+":"+r.name,r.file=this.path),r.fatal=!1,this.messages.push(r),r}toString(e){return void 0===this.value?"":"string"==typeof this.value?this.value:new TextDecoder(e||void 0).decode(this.value)}}function nA(e,t){if(e&&e.includes(nT.sep))throw Error("`"+t+"` cannot be a path: did not expect `"+nT.sep+"`")}function nq(e,t){if(!e)throw Error("`"+t+"` cannot be empty")}function nR(e,t){if(!e)throw Error("Setting `"+t+"` requires `path` to be set too")}let nO=function(e){let t=this.constructor.prototype,n=t[e],r=function(){return n.apply(r,arguments)};return Object.setPrototypeOf(r,t),r},nB={}.hasOwnProperty;class nF extends nO{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=function(){let e=[],t={run:function(...t){let n=-1,r=t.pop();if("function"!=typeof r)throw TypeError("Expected function as last argument, not "+r);(function i(o,...l){let a=e[++n],s=-1;if(o){r(o);return}for(;++s<t.length;)(null===l[s]||void 0===l[s])&&(l[s]=t[s]);t=l,a?(function(e,t){let n;return function(...t){let o;let l=e.length>t.length;l&&t.push(r);try{o=e.apply(this,t)}catch(e){if(l&&n)throw e;return r(e)}l||(o&&o.then&&"function"==typeof o.then?o.then(i,r):o instanceof Error?r(o):i(o))};function r(e,...i){n||(n=!0,t(e,...i))}function i(e){r(null,e)}})(a,i)(...l):r(null,...l)})(null,...t)},use:function(n){if("function"!=typeof n)throw TypeError("Expected `middelware` to be a function, not "+n);return e.push(n),t}};return t}()}copy(){let e=new nF,t=-1;for(;++t<this.attachers.length;){let n=this.attachers[t];e.use(...n)}return e.data(nD(!0,{},this.namespace)),e}data(e,t){return"string"==typeof e?2==arguments.length?(nZ("data",this.frozen),this.namespace[e]=t,this):nB.call(this.namespace,e)&&this.namespace[e]||void 0:e?(nZ("data",this.frozen),this.namespace=e,this):this.namespace}freeze(){if(this.frozen)return this;for(;++this.freezeIndex<this.attachers.length;){let[e,...t]=this.attachers[this.freezeIndex];if(!1===t[0])continue;!0===t[0]&&(t[0]=void 0);let n=e.call(this,...t);"function"==typeof n&&this.transformers.use(n)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(e){this.freeze();let t=nW(e),n=this.parser||this.Parser;return nV("parse",n),n(String(t),t)}process(e,t){let n=this;return this.freeze(),nV("process",this.parser||this.Parser),nU("process",this.compiler||this.Compiler),t?r(void 0,t):new Promise(r);function r(r,i){let o=nW(e),l=n.parse(o);function a(e,n){e||!n?i(e):r?r(n):t(void 0,n)}n.run(l,o,function(e,t,r){if(e||!t||!r)return a(e);let i=n.stringify(t,r);"string"==typeof i||i&&"object"==typeof i&&"byteLength"in i&&"byteOffset"in i?r.value=i:r.result=i,a(e,r)})}}processSync(e){let t,n=!1;return this.freeze(),nV("processSync",this.parser||this.Parser),nU("processSync",this.compiler||this.Compiler),this.process(e,function(e,r){n=!0,nE(e),t=r}),nG("processSync","process",n),t}run(e,t,n){n$(e),this.freeze();let r=this.transformers;return n||"function"!=typeof t||(n=t,t=void 0),n?i(void 0,n):new Promise(i);function i(i,o){let l=nW(t);r.run(e,l,function(t,r,l){let a=r||e;t?o(t):i?i(a):n(void 0,a,l)})}}runSync(e,t){let n,r=!1;return this.run(e,t,function(e,t){nE(e),n=t,r=!0}),nG("runSync","run",r),n}stringify(e,t){this.freeze();let n=nW(t),r=this.compiler||this.Compiler;return nU("stringify",r),n$(e),r(e,n)}use(e,...t){let n=this.attachers,r=this.namespace;if(nZ("use",this.frozen),null==e);else if("function"==typeof e)l(e,t);else if("object"==typeof e)Array.isArray(e)?o(e):i(e);else throw TypeError("Expected usable value, not `"+e+"`");return this;function i(e){if(!("plugins"in e)&&!("settings"in e))throw Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");o(e.plugins),e.settings&&(r.settings=nD(!0,r.settings,e.settings))}function o(e){let t=-1;if(null==e);else if(Array.isArray(e))for(;++t<e.length;)!function(e){if("function"==typeof e)l(e,[]);else if("object"==typeof e){if(Array.isArray(e)){let[t,...n]=e;l(t,n)}else i(e)}else throw TypeError("Expected usable value, not `"+e+"`")}(e[t]);else throw TypeError("Expected a list of plugins, not `"+e+"`")}function l(e,t){let r=-1,i=-1;for(;++r<n.length;)if(n[r][0]===e){i=r;break}if(-1===i)n.push([e,...t]);else if(t.length>0){let[r,...o]=t,l=n[i][1];nL(l)&&nL(r)&&(r=nD(!0,l,r)),n[i]=[e,r,...o]}}}}let nH=new nF().freeze();function nV(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `parser`")}function nU(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `compiler`")}function nZ(e,t){if(t)throw Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function n$(e){if(!nL(e)||"string"!=typeof e.type)throw TypeError("Expected node, got `"+e+"`")}function nG(e,t,n){if(!n)throw Error("`"+e+"` finished async. Use `"+t+"` instead")}function nW(e){return e&&"object"==typeof e&&"message"in e&&"messages"in e?e:new nz(e)}let nK=[],nY={allowDangerousHtml:!0},nJ=/^(https?|ircs?|mailto|xmpp)$/i,nQ=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function nX(e){let t=e.allowedElements,n=e.allowElement,r=e.children||"",i=e.className,l=e.components,a=e.disallowedElements,s=e.rehypePlugins||nK,c=e.remarkPlugins||nK,u=e.remarkRehypeOptions?{...e.remarkRehypeOptions,...nY}:nY,d=e.skipHtml,p=e.unwrapDisallowed,h=e.urlTransform||n1,f=nH().use(t5).use(c).use(nj,u).use(s),m=new nz;for(let t of("string"==typeof r&&(m.value=r),nQ))Object.hasOwn(e,t.from)&&(t.from,t.to&&t.to,t.id);let g=f.parse(m),v=f.runSync(g,m);return i&&(v={type:"element",tagName:"div",properties:{className:i},children:"root"===v.type?v.children:[v]}),nh(v,function(e,r,i){if("raw"===e.type&&i&&"number"==typeof r)return d?i.children.splice(r,1):i.children[r]={type:"text",value:e.value},r;if("element"===e.type){let t;for(t in eP)if(Object.hasOwn(eP,t)&&Object.hasOwn(e.properties,t)){let n=e.properties[t],r=eP[t];(null===r||r.includes(e.tagName))&&(e.properties[t]=h(String(n||""),t,e))}}if("element"===e.type){let o=t?!t.includes(e.tagName):!!a&&a.includes(e.tagName);if(!o&&n&&"number"==typeof r&&(o=!n(e,r,i)),o&&i&&"number"==typeof r)return p&&e.children?i.children.splice(r,1,...e.children):i.children.splice(r,1),r}}),function(e,t){var n,r,i;let o;if(!t||void 0===t.Fragment)throw TypeError("Expected `Fragment` in options");let l=t.filePath||void 0;if(t.development){if("function"!=typeof t.jsxDEV)throw TypeError("Expected `jsxDEV` in options when `development: true`");n=t.jsxDEV,o=function(e,t,r,i){let o=Array.isArray(r.children),a=ep(e);return n(t,r,i,o,{columnNumber:a?a.column-1:void 0,fileName:l,lineNumber:a?a.line:void 0},void 0)}}else{if("function"!=typeof t.jsx)throw TypeError("Expected `jsx` in production options");if("function"!=typeof t.jsxs)throw TypeError("Expected `jsxs` in production options");r=t.jsx,i=t.jsxs,o=function(e,t,n,o){let l=Array.isArray(n.children)?i:r;return o?l(t,n,o):l(t,n)}}let a={Fragment:t.Fragment,ancestors:[],components:t.components||{},create:o,elementAttributeNameCase:t.elementAttributeNameCase||"react",evaluater:t.createEvaluater?t.createEvaluater():void 0,filePath:l,ignoreInvalidStyle:t.ignoreInvalidStyle||!1,passKeys:!1!==t.passKeys,passNode:t.passNode||!1,schema:"svg"===t.space?en:et,stylePropertyNameCase:t.stylePropertyNameCase||"dom",tableCellAlignToStyle:!1!==t.tableCellAlignToStyle},s=ej(a,e,void 0);return s&&"string"!=typeof s?s:a.create(e,a.Fragment,{children:s||void 0},void 0)}(v,{Fragment:o.Fragment,components:l,ignoreInvalidStyle:!0,jsx:o.jsx,jsxs:o.jsxs,passKeys:!0,passNode:!0})}function n1(e){let t=e.indexOf(":"),n=e.indexOf("?"),r=e.indexOf("#"),i=e.indexOf("/");return -1===t||-1!==i&&t>i||-1!==n&&t>n||-1!==r&&t>r||nJ.test(e.slice(0,t))?e:""}var n0=n(19547),n2=n(11891),n3=n(98181),n4=n(33519),n5=n(70178),n6=n(88118),n7=n(46226);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n9=(0,h.Z)("Smile",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 14s1.5 2 4 2 4-2 4-2",key:"1y1vjs"}],["line",{x1:"9",x2:"9.01",y1:"9",y2:"9",key:"yxxnd0"}],["line",{x1:"15",x2:"15.01",y1:"9",y2:"9",key:"1p4y9e"}]]);function n8(e){return e&&e.__esModule?e.default:e}function re(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var rt,rn,rr,ri,ro,rl,ra={},rs=[],rc=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function ru(e,t){for(var n in t)e[n]=t[n];return e}function rd(e){var t=e.parentNode;t&&t.removeChild(e)}function rp(e,t,n){var r,i,o,l={};for(o in t)"key"==o?r=t[o]:"ref"==o?i=t[o]:l[o]=t[o];if(arguments.length>2&&(l.children=arguments.length>3?rt.call(arguments,2):n),"function"==typeof e&&null!=e.defaultProps)for(o in e.defaultProps)void 0===l[o]&&(l[o]=e.defaultProps[o]);return rh(e,l,r,i,null)}function rh(e,t,n,r,i){var o={type:e,props:t,key:n,ref:r,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:null==i?++rr:i};return null==i&&null!=rn.vnode&&rn.vnode(o),o}function rf(){return{current:null}}function rm(e){return e.children}function rg(e,t){this.props=e,this.context=t}function rv(e,t){if(null==t)return e.__?rv(e.__,e.__.__k.indexOf(e)+1):null;for(var n;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e)return n.__e;return"function"==typeof e.type?rv(e):null}function ry(e){(!e.__d&&(e.__d=!0)&&ri.push(e)&&!rb.__r++||rl!==rn.debounceRendering)&&((rl=rn.debounceRendering)||ro)(rb)}function rb(){for(var e;rb.__r=ri.length;)e=ri.sort(function(e,t){return e.__v.__b-t.__v.__b}),ri=[],e.some(function(e){var t,n,r,i,o;e.__d&&(i=(r=e.__v).__e,(o=e.__P)&&(t=[],(n=ru({},r)).__v=r.__v+1,rE(o,r,n,e.__n,void 0!==o.ownerSVGElement,null!=r.__h?[i]:null,t,null==i?rv(r):i,r.__h),rD(t,r),r.__e!=i&&function e(t){var n,r;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,n=0;n<t.__k.length;n++)if(null!=(r=t.__k[n])&&null!=r.__e){t.__e=t.__c.base=r.__e;break}return e(t)}}(r)))})}function rx(e,t,n,r,i,o,l,a,s,c){var u,d,p,h,f,m,g,v=r&&r.__k||rs,y=v.length;for(n.__k=[],u=0;u<t.length;u++)if(null!=(h=n.__k[u]=null==(h=t[u])||"boolean"==typeof h?null:"string"==typeof h||"number"==typeof h||"bigint"==typeof h?rh(null,h,null,null,h):Array.isArray(h)?rh(rm,{children:h},null,null,null):h.__b>0?rh(h.type,h.props,h.key,null,h.__v):h)){if(h.__=n,h.__b=n.__b+1,null===(p=v[u])||p&&h.key==p.key&&h.type===p.type)v[u]=void 0;else for(d=0;d<y;d++){if((p=v[d])&&h.key==p.key&&h.type===p.type){v[d]=void 0;break}p=null}rE(e,h,p=p||ra,i,o,l,a,s,c),f=h.__e,(d=h.ref)&&p.ref!=d&&(g||(g=[]),p.ref&&g.push(p.ref,null,h),g.push(d,h.__c||f,h)),null!=f?(null==m&&(m=f),"function"==typeof h.type&&h.__k===p.__k?h.__d=s=function e(t,n,r){for(var i,o=t.__k,l=0;o&&l<o.length;l++)(i=o[l])&&(i.__=t,n="function"==typeof i.type?e(i,n,r):rw(r,i,i,o,i.__e,n));return n}(h,s,e):s=rw(e,h,p,v,f,s),"function"==typeof n.type&&(n.__d=s)):s&&p.__e==s&&s.parentNode!=e&&(s=rv(p))}for(n.__e=m,u=y;u--;)null!=v[u]&&("function"==typeof n.type&&null!=v[u].__e&&v[u].__e==n.__d&&(n.__d=rv(r,u+1)),function e(t,n,r){var i,o;if(rn.unmount&&rn.unmount(t),(i=t.ref)&&(i.current&&i.current!==t.__e||rL(i,null,n)),null!=(i=t.__c)){if(i.componentWillUnmount)try{i.componentWillUnmount()}catch(e){rn.__e(e,n)}i.base=i.__P=null}if(i=t.__k)for(o=0;o<i.length;o++)i[o]&&e(i[o],n,"function"!=typeof t.type);r||null==t.__e||rd(t.__e),t.__e=t.__d=void 0}(v[u],v[u]));if(g)for(u=0;u<g.length;u++)rL(g[u],g[++u],g[++u])}function rk(e,t){return t=t||[],null==e||"boolean"==typeof e||(Array.isArray(e)?e.some(function(e){rk(e,t)}):t.push(e)),t}function rw(e,t,n,r,i,o){var l,a,s;if(void 0!==t.__d)l=t.__d,t.__d=void 0;else if(null==n||i!=o||null==i.parentNode)e:if(null==o||o.parentNode!==e)e.appendChild(i),l=null;else{for(a=o,s=0;(a=a.nextSibling)&&s<r.length;s+=2)if(a==i)break e;e.insertBefore(i,o),l=o}return void 0!==l?l:i.nextSibling}function r_(e,t,n){"-"===t[0]?e.setProperty(t,n):e[t]=null==n?"":"number"!=typeof n||rc.test(t)?n:n+"px"}function rC(e,t,n,r,i){var o;e:if("style"===t){if("string"==typeof n)e.style.cssText=n;else{if("string"==typeof r&&(e.style.cssText=r=""),r)for(t in r)n&&t in n||r_(e.style,t,"");if(n)for(t in n)r&&n[t]===r[t]||r_(e.style,t,n[t])}}else if("o"===t[0]&&"n"===t[1])o=t!==(t=t.replace(/Capture$/,"")),t=t.toLowerCase() in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+o]=n,n?r||e.addEventListener(t,o?rj:rS,o):e.removeEventListener(t,o?rj:rS,o);else if("dangerouslySetInnerHTML"!==t){if(i)t=t.replace(/xlink[H:h]/,"h").replace(/sName$/,"s");else if("href"!==t&&"list"!==t&&"form"!==t&&"tabIndex"!==t&&"download"!==t&&t in e)try{e[t]=null==n?"":n;break e}catch(e){}"function"==typeof n||(null!=n&&(!1!==n||"a"===t[0]&&"r"===t[1])?e.setAttribute(t,n):e.removeAttribute(t))}}function rS(e){this.l[e.type+!1](rn.event?rn.event(e):e)}function rj(e){this.l[e.type+!0](rn.event?rn.event(e):e)}function rE(e,t,n,r,i,o,l,a,s){var c,u,d,p,h,f,m,g,v,y,b,x=t.type;if(void 0!==t.constructor)return null;null!=n.__h&&(s=n.__h,a=t.__e=n.__e,t.__h=null,o=[a]),(c=rn.__b)&&c(t);try{e:if("function"==typeof x){if(g=t.props,v=(c=x.contextType)&&r[c.__c],y=c?v?v.props.value:c.__:r,n.__c?m=(u=t.__c=n.__c).__=u.__E:("prototype"in x&&x.prototype.render?t.__c=u=new x(g,y):(t.__c=u=new rg(g,y),u.constructor=x,u.render=rT),v&&v.sub(u),u.props=g,u.state||(u.state={}),u.context=y,u.__n=r,d=u.__d=!0,u.__h=[]),null==u.__s&&(u.__s=u.state),null!=x.getDerivedStateFromProps&&(u.__s==u.state&&(u.__s=ru({},u.__s)),ru(u.__s,x.getDerivedStateFromProps(g,u.__s))),p=u.props,h=u.state,d)null==x.getDerivedStateFromProps&&null!=u.componentWillMount&&u.componentWillMount(),null!=u.componentDidMount&&u.__h.push(u.componentDidMount);else{if(null==x.getDerivedStateFromProps&&g!==p&&null!=u.componentWillReceiveProps&&u.componentWillReceiveProps(g,y),!u.__e&&null!=u.shouldComponentUpdate&&!1===u.shouldComponentUpdate(g,u.__s,y)||t.__v===n.__v){u.props=g,u.state=u.__s,t.__v!==n.__v&&(u.__d=!1),u.__v=t,t.__e=n.__e,t.__k=n.__k,t.__k.forEach(function(e){e&&(e.__=t)}),u.__h.length&&l.push(u);break e}null!=u.componentWillUpdate&&u.componentWillUpdate(g,u.__s,y),null!=u.componentDidUpdate&&u.__h.push(function(){u.componentDidUpdate(p,h,f)})}u.context=y,u.props=g,u.state=u.__s,(c=rn.__r)&&c(t),u.__d=!1,u.__v=t,u.__P=e,c=u.render(u.props,u.state,u.context),u.state=u.__s,null!=u.getChildContext&&(r=ru(ru({},r),u.getChildContext())),d||null==u.getSnapshotBeforeUpdate||(f=u.getSnapshotBeforeUpdate(p,h)),b=null!=c&&c.type===rm&&null==c.key?c.props.children:c,rx(e,Array.isArray(b)?b:[b],t,n,r,i,o,l,a,s),u.base=t.__e,t.__h=null,u.__h.length&&l.push(u),m&&(u.__E=u.__=null),u.__e=!1}else null==o&&t.__v===n.__v?(t.__k=n.__k,t.__e=n.__e):t.__e=function(e,t,n,r,i,o,l,a){var s,c,u,d=n.props,p=t.props,h=t.type,f=0;if("svg"===h&&(i=!0),null!=o){for(;f<o.length;f++)if((s=o[f])&&"setAttribute"in s==!!h&&(h?s.localName===h:3===s.nodeType)){e=s,o[f]=null;break}}if(null==e){if(null===h)return document.createTextNode(p);e=i?document.createElementNS("http://www.w3.org/2000/svg",h):document.createElement(h,p.is&&p),o=null,a=!1}if(null===h)d===p||a&&e.data===p||(e.data=p);else{if(o=o&&rt.call(e.childNodes),c=(d=n.props||ra).dangerouslySetInnerHTML,u=p.dangerouslySetInnerHTML,!a){if(null!=o)for(d={},f=0;f<e.attributes.length;f++)d[e.attributes[f].name]=e.attributes[f].value;(u||c)&&(u&&(c&&u.__html==c.__html||u.__html===e.innerHTML)||(e.innerHTML=u&&u.__html||""))}if(function(e,t,n,r,i){var o;for(o in n)"children"===o||"key"===o||o in t||rC(e,o,null,n[o],r);for(o in t)i&&"function"!=typeof t[o]||"children"===o||"key"===o||"value"===o||"checked"===o||n[o]===t[o]||rC(e,o,t[o],n[o],r)}(e,p,d,i,a),u)t.__k=[];else if(rx(e,Array.isArray(f=t.props.children)?f:[f],t,n,r,i&&"foreignObject"!==h,o,l,o?o[0]:n.__k&&rv(n,0),a),null!=o)for(f=o.length;f--;)null!=o[f]&&rd(o[f]);a||("value"in p&&void 0!==(f=p.value)&&(f!==d.value||f!==e.value||"progress"===h&&!f)&&rC(e,"value",f,d.value,!1),"checked"in p&&void 0!==(f=p.checked)&&f!==e.checked&&rC(e,"checked",f,d.checked,!1))}return e}(n.__e,t,n,r,i,o,l,s);(c=rn.diffed)&&c(t)}catch(e){t.__v=null,(s||null!=o)&&(t.__e=a,t.__h=!!s,o[o.indexOf(a)]=null),rn.__e(e,t,n)}}function rD(e,t){rn.__c&&rn.__c(t,e),e.some(function(t){try{e=t.__h,t.__h=[],e.some(function(e){e.call(t)})}catch(e){rn.__e(e,t.__v)}})}function rL(e,t,n){try{"function"==typeof e?e(t):e.current=t}catch(e){rn.__e(e,n)}}function rT(e,t,n){return this.constructor(e,n)}function rN(e,t,n){var r,i,o;rn.__&&rn.__(e,t),i=(r="function"==typeof n)?null:n&&n.__k||t.__k,o=[],rE(t,e=(!r&&n||t).__k=rp(rm,null,[e]),i||ra,ra,void 0!==t.ownerSVGElement,!r&&n?[n]:i?null:t.firstChild?rt.call(t.childNodes):null,o,!r&&n?n:i?i.__e:t.firstChild,r),rD(o,e)}rt=rs.slice,rn={__e:function(e,t){for(var n,r,i;t=t.__;)if((n=t.__c)&&!n.__)try{if((r=n.constructor)&&null!=r.getDerivedStateFromError&&(n.setState(r.getDerivedStateFromError(e)),i=n.__d),null!=n.componentDidCatch&&(n.componentDidCatch(e),i=n.__d),i)return n.__E=n}catch(t){e=t}throw e}},rr=0,rg.prototype.setState=function(e,t){var n;n=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=ru({},this.state),"function"==typeof e&&(e=e(ru({},n),this.props)),e&&ru(n,e),null!=e&&this.__v&&(t&&this.__h.push(t),ry(this))},rg.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),ry(this))},rg.prototype.render=rm,ri=[],ro="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,rb.__r=0;var rM=0;function rI(e,t,n,r,i){var o,l,a={};for(l in t)"ref"==l?o=t[l]:a[l]=t[l];var s={type:e,props:a,key:n,ref:o,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:--rM,__source:r,__self:i};if("function"==typeof e&&(o=e.defaultProps))for(l in o)void 0===a[l]&&(a[l]=o[l]);return rn.vnode&&rn.vnode(s),s}var rP={set:function(e,t){try{window.localStorage[`emoji-mart.${e}`]=JSON.stringify(t)}catch(e){}},get:function(e){try{let t=window.localStorage[`emoji-mart.${e}`];if(t)return JSON.parse(t)}catch(e){}}};let rz=new Map,rA=[{v:15,emoji:"\uD83E\uDEE8"},{v:14,emoji:"\uD83E\uDEE0"},{v:13.1,emoji:"\uD83D\uDE36‍\uD83C\uDF2B️"},{v:13,emoji:"\uD83E\uDD78"},{v:12.1,emoji:"\uD83E\uDDD1‍\uD83E\uDDB0"},{v:12,emoji:"\uD83E\uDD71"},{v:11,emoji:"\uD83E\uDD70"},{v:5,emoji:"\uD83E\uDD29"},{v:4,emoji:"\uD83D\uDC71‍♀️"},{v:3,emoji:"\uD83E\uDD23"},{v:2,emoji:"\uD83D\uDC4B\uD83C\uDFFB"},{v:1,emoji:"\uD83D\uDE43"}];function rq(e){if(rz.has(e))return rz.get(e);let t=rR(e);return rz.set(e,t),t}let rR=(()=>{let e=null;try{navigator.userAgent.includes("jsdom")||(e=document.createElement("canvas").getContext("2d",{willReadFrequently:!0}))}catch{}return e?(e.font=Math.floor(12.5)+"px Arial, Sans-Serif",e.textBaseline="top",e.canvas.width=40,e.canvas.height=25,t=>{e.clearRect(0,0,40,25),e.fillStyle="#FF0000",e.fillText(t,0,22),e.fillStyle="#0000FF",e.fillText(t,20,22);let n=e.getImageData(0,0,20,25).data,r=n.length,i=0;for(;i<r&&!n[i+3];i+=4);if(i>=r)return!1;let o=20+i/4%20,l=Math.floor(i/4/20),a=e.getImageData(o,l,1,1).data;return n[i]===a[0]&&n[i+2]===a[2]&&!(e.measureText(t).width>=20)}):()=>!1})();var rO={latestVersion:function(){for(let{v:e,emoji:t}of rA)if(rq(t))return e},noCountryFlags:function(){return!rq("\uD83C\uDDE8\uD83C\uDDE6")}};let rB=["+1","grinning","kissing_heart","heart_eyes","laughing","stuck_out_tongue_winking_eye","sweat_smile","joy","scream","disappointed","unamused","weary","sob","sunglasses","heart"],rF=null;var rH={add:function(e){rF||(rF=rP.get("frequently")||{});let t=e.id||e;t&&(rF[t]||(rF[t]=0),rF[t]+=1,rP.set("last",t),rP.set("frequently",rF))},get:function({maxFrequentRows:e,perLine:t}){if(!e)return[];rF||(rF=rP.get("frequently"));let n=[];if(!rF){for(let e in rF={},rB.slice(0,t)){let r=rB[e];rF[r]=t-e,n.push(r)}return n}let r=e*t,i=rP.get("last");for(let e in rF)n.push(e);if(n.sort((e,t)=>{let n=rF[t],r=rF[e];return n==r?e.localeCompare(t):n-r}),n.length>r){let e=n.slice(r);for(let t of(n=n.slice(0,r),e))t!=i&&delete rF[t];i&&-1==n.indexOf(i)&&(delete rF[n[n.length-1]],n.splice(-1,1,i)),rP.set("frequently",rF)}return n}},rV={};rV=JSON.parse('{"search":"Search","search_no_results_1":"Oh no!","search_no_results_2":"That emoji couldn’t be found","pick":"Pick an emoji…","add_custom":"Add custom emoji","categories":{"activity":"Activity","custom":"Custom","flags":"Flags","foods":"Food & Drink","frequent":"Frequently used","nature":"Animals & Nature","objects":"Objects","people":"Smileys & People","places":"Travel & Places","search":"Search Results","symbols":"Symbols"},"skins":{"1":"Default","2":"Light","3":"Medium-Light","4":"Medium","5":"Medium-Dark","6":"Dark","choose":"Choose default skin tone"}}');var rU={autoFocus:{value:!1},dynamicWidth:{value:!1},emojiButtonColors:{value:null},emojiButtonRadius:{value:"100%"},emojiButtonSize:{value:36},emojiSize:{value:24},emojiVersion:{value:15,choices:[1,2,3,4,5,11,12,12.1,13,13.1,14,15]},exceptEmojis:{value:[]},icons:{value:"auto",choices:["auto","outline","solid"]},locale:{value:"en",choices:["en","ar","be","cs","de","es","fa","fi","fr","hi","it","ja","ko","nl","pl","pt","ru","sa","tr","uk","vi","zh"]},maxFrequentRows:{value:4},navPosition:{value:"top",choices:["top","bottom","none"]},noCountryFlags:{value:!1},noResultsEmoji:{value:null},perLine:{value:9},previewEmoji:{value:null},previewPosition:{value:"bottom",choices:["top","bottom","none"]},searchPosition:{value:"sticky",choices:["sticky","static","none"]},set:{value:"native",choices:["native","apple","facebook","google","twitter"]},skin:{value:1,choices:[1,2,3,4,5,6]},skinTonePosition:{value:"preview",choices:["preview","search","none"]},theme:{value:"auto",choices:["auto","light","dark"]},categories:null,categoryIcons:null,custom:null,data:null,i18n:null,getImageURL:null,getSpritesheetURL:null,onAddCustomEmoji:null,onClickOutside:null,onEmojiSelect:null,stickySearch:{deprecated:!0,value:!0}};let rZ=null,r$=null,rG={};async function rW(e){if(rG[e])return rG[e];let t=await fetch(e),n=await t.json();return rG[e]=n,n}let rK=null,rY=null,rJ=!1;function rQ(e,{caller:t}={}){return rK||(rK=new Promise(e=>{rY=e})),e?rX(e):t&&!rJ&&console.warn(`\`${t}\` requires data to be initialized first. Promise will be pending until \`init\` is called.`),rK}async function rX(e){rJ=!0;let{emojiVersion:t,set:n,locale:r}=e;if(t||(t=rU.emojiVersion.value),n||(n=rU.set.value),r||(r=rU.locale.value),r$)r$.categories=r$.categories.filter(e=>!e.name);else{for(let r in(r$=("function"==typeof e.data?await e.data():e.data)||await rW(`https://cdn.jsdelivr.net/npm/@emoji-mart/data@latest/sets/${t}/${n}.json`)).emoticons={},r$.natives={},r$.categories.unshift({id:"frequent",emojis:[]}),r$.aliases){let e=r$.aliases[r],t=r$.emojis[e];t&&(t.aliases||(t.aliases=[]),t.aliases.push(r))}r$.originalCategories=r$.categories}if(rZ=("function"==typeof e.i18n?await e.i18n():e.i18n)||("en"==r?n8(rV):await rW(`https://cdn.jsdelivr.net/npm/@emoji-mart/data@latest/i18n/${r}.json`)),e.custom)for(let t in e.custom){t=parseInt(t);let n=e.custom[t],r=e.custom[t-1];if(n.emojis&&n.emojis.length)for(let e of(n.id||(n.id=`custom_${t+1}`),n.name||(n.name=rZ.categories.custom),r&&!n.icon&&(n.target=r.target||r),r$.categories.push(n),n.emojis))r$.emojis[e.id]=e}e.categories&&(r$.categories=r$.originalCategories.filter(t=>-1!=e.categories.indexOf(t.id)).sort((t,n)=>e.categories.indexOf(t.id)-e.categories.indexOf(n.id)));let i=null,o=null;"native"==n&&(i=rO.latestVersion(),o=e.noCountryFlags||rO.noCountryFlags());let l=r$.categories.length,a=!1;for(;l--;){let t=r$.categories[l];if("frequent"==t.id){let{maxFrequentRows:n,perLine:r}=e;n=n>=0?n:rU.maxFrequentRows.value,r||(r=rU.perLine.value),t.emojis=rH.get({maxFrequentRows:n,perLine:r})}if(!t.emojis||!t.emojis.length){r$.categories.splice(l,1);continue}let{categoryIcons:n}=e;if(n){let e=n[t.id];e&&!t.icon&&(t.icon=e)}let r=t.emojis.length;for(;r--;){let n=t.emojis[r],l=n.id?n:r$.emojis[n],s=()=>{t.emojis.splice(r,1)};if(!l||e.exceptEmojis&&e.exceptEmojis.includes(l.id)||i&&l.version>i||o&&"flags"==t.id&&!r4.includes(l.id)){s();continue}if(!l.search){if(a=!0,l.search=","+[[l.id,!1],[l.name,!0],[l.keywords,!1],[l.emoticons,!1]].map(([e,t])=>{if(e)return(Array.isArray(e)?e:[e]).map(e=>(t?e.split(/[-|_|\s]+/):[e]).map(e=>e.toLowerCase())).flat()}).flat().filter(e=>e&&e.trim()).join(","),l.emoticons)for(let e of l.emoticons)r$.emoticons[e]||(r$.emoticons[e]=l.id);let e=0;for(let t of l.skins){if(!t)continue;e++;let{native:n}=t;n&&(r$.natives[n]=l.id,l.search+=`,${n}`);let r=1==e?"":`:skin-tone-${e}:`;t.shortcodes=`:${l.id}:${r}`}}}}a&&r3.reset(),rY()}function r1(e,t,n){e||(e={});let r={};for(let i in t)r[i]=r0(i,e,t,n);return r}function r0(e,t,n,r){let i=n[e],o=r&&r.getAttribute(e)||(null!=t[e]&&void 0!=t[e]?t[e]:null);return i&&(null!=o&&i.value&&typeof i.value!=typeof o&&(o="boolean"==typeof i.value?"false"!=o:i.value.constructor(o)),i.transform&&o&&(o=i.transform(o)),(null==o||i.choices&&-1==i.choices.indexOf(o))&&(o=i.value)),o}let r2=null;var r3={search:async function(e,{maxResults:t,caller:n}={}){let r,i;if(!e||!e.trim().length)return null;t||(t=90),await rQ(null,{caller:n||"SearchIndex.search"});let o=e.toLowerCase().replace(/(\w)-/,"$1 ").split(/[\s|,]+/).filter((e,t,n)=>e.trim()&&n.indexOf(e)==t);if(!o.length)return;let l=r2||(r2=Object.values(r$.emojis));for(let e of o){if(!l.length)break;for(let t of(r=[],i={},l)){if(!t.search)continue;let n=t.search.indexOf(`,${e}`);-1!=n&&(r.push(t),i[t.id]||(i[t.id]=0),i[t.id]+=t.id==e?0:n+1)}l=r}return r.length<2||(r.sort((e,t)=>{let n=i[e.id],r=i[t.id];return n==r?e.id.localeCompare(t.id):n-r}),r.length>t&&(r=r.slice(0,t))),r},get:function(e){return e.id?e:r$.emojis[e]||r$.emojis[r$.aliases[e]]||r$.emojis[r$.natives[e]]},reset:function(){r2=null},SHORTCODES_REGEX:/^(?:\:([^\:]+)\:)(?:\:skin-tone-(\d)\:)?$/};let r4=["checkered_flag","crossed_flags","pirate_flag","rainbow-flag","transgender_flag","triangular_flag_on_post","waving_black_flag","waving_white_flag"];async function r5(e=1){for(let t in[...Array(e).keys()])await new Promise(requestAnimationFrame)}var r6={categories:{activity:{outline:rI("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:rI("path",{d:"M12 0C5.373 0 0 5.372 0 12c0 6.627 5.373 12 12 12 6.628 0 12-5.373 12-12 0-6.628-5.372-12-12-12m9.949 11H17.05c.224-2.527 1.232-4.773 1.968-6.113A9.966 9.966 0 0 1 21.949 11M13 11V2.051a9.945 9.945 0 0 1 4.432 1.564c-.858 1.491-2.156 4.22-2.392 7.385H13zm-2 0H8.961c-.238-3.165-1.536-5.894-2.393-7.385A9.95 9.95 0 0 1 11 2.051V11zm0 2v8.949a9.937 9.937 0 0 1-4.432-1.564c.857-1.492 2.155-4.221 2.393-7.385H11zm4.04 0c.236 3.164 1.534 5.893 2.392 7.385A9.92 9.92 0 0 1 13 21.949V13h2.04zM4.982 4.887C5.718 6.227 6.726 8.473 6.951 11h-4.9a9.977 9.977 0 0 1 2.931-6.113M2.051 13h4.9c-.226 2.527-1.233 4.771-1.969 6.113A9.972 9.972 0 0 1 2.051 13m16.967 6.113c-.735-1.342-1.744-3.586-1.968-6.113h4.899a9.961 9.961 0 0 1-2.931 6.113"})}),solid:rI("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",children:rI("path",{d:"M16.17 337.5c0 44.98 7.565 83.54 13.98 107.9C35.22 464.3 50.46 496 174.9 496c9.566 0 19.59-.4707 29.84-1.271L17.33 307.3C16.53 317.6 16.17 327.7 16.17 337.5zM495.8 174.5c0-44.98-7.565-83.53-13.98-107.9c-4.688-17.54-18.34-31.23-36.04-35.95C435.5 27.91 392.9 16 337 16c-9.564 0-19.59 .4707-29.84 1.271l187.5 187.5C495.5 194.4 495.8 184.3 495.8 174.5zM26.77 248.8l236.3 236.3c142-36.1 203.9-150.4 222.2-221.1L248.9 26.87C106.9 62.96 45.07 177.2 26.77 248.8zM256 335.1c0 9.141-7.474 16-16 16c-4.094 0-8.188-1.564-11.31-4.689L164.7 283.3C161.6 280.2 160 276.1 160 271.1c0-8.529 6.865-16 16-16c4.095 0 8.189 1.562 11.31 4.688l64.01 64C254.4 327.8 256 331.9 256 335.1zM304 287.1c0 9.141-7.474 16-16 16c-4.094 0-8.188-1.564-11.31-4.689L212.7 235.3C209.6 232.2 208 228.1 208 223.1c0-9.141 7.473-16 16-16c4.094 0 8.188 1.562 11.31 4.688l64.01 64.01C302.5 279.8 304 283.9 304 287.1zM256 175.1c0-9.141 7.473-16 16-16c4.094 0 8.188 1.562 11.31 4.688l64.01 64.01c3.125 3.125 4.688 7.219 4.688 11.31c0 9.133-7.468 16-16 16c-4.094 0-8.189-1.562-11.31-4.688l-64.01-64.01C257.6 184.2 256 180.1 256 175.1z"})})},custom:rI("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512",children:rI("path",{d:"M417.1 368c-5.937 10.27-16.69 16-27.75 16c-5.422 0-10.92-1.375-15.97-4.281L256 311.4V448c0 17.67-14.33 32-31.1 32S192 465.7 192 448V311.4l-118.3 68.29C68.67 382.6 63.17 384 57.75 384c-11.06 0-21.81-5.734-27.75-16c-8.828-15.31-3.594-34.88 11.72-43.72L159.1 256L41.72 187.7C26.41 178.9 21.17 159.3 29.1 144C36.63 132.5 49.26 126.7 61.65 128.2C65.78 128.7 69.88 130.1 73.72 132.3L192 200.6V64c0-17.67 14.33-32 32-32S256 46.33 256 64v136.6l118.3-68.29c3.838-2.213 7.939-3.539 12.07-4.051C398.7 126.7 411.4 132.5 417.1 144c8.828 15.31 3.594 34.88-11.72 43.72L288 256l118.3 68.28C421.6 333.1 426.8 352.7 417.1 368z"})}),flags:{outline:rI("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:rI("path",{d:"M0 0l6.084 24H8L1.916 0zM21 5h-4l-1-4H4l3 12h3l1 4h13L21 5zM6.563 3h7.875l2 8H8.563l-2-8zm8.832 10l-2.856 1.904L12.063 13h3.332zM19 13l-1.5-6h1.938l2 8H16l3-2z"})}),solid:rI("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",children:rI("path",{d:"M64 496C64 504.8 56.75 512 48 512h-32C7.25 512 0 504.8 0 496V32c0-17.75 14.25-32 32-32s32 14.25 32 32V496zM476.3 0c-6.365 0-13.01 1.35-19.34 4.233c-45.69 20.86-79.56 27.94-107.8 27.94c-59.96 0-94.81-31.86-163.9-31.87C160.9 .3055 131.6 4.867 96 15.75v350.5c32-9.984 59.87-14.1 84.85-14.1c73.63 0 124.9 31.78 198.6 31.78c31.91 0 68.02-5.971 111.1-23.09C504.1 355.9 512 344.4 512 332.1V30.73C512 11.1 495.3 0 476.3 0z"})})},foods:{outline:rI("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:rI("path",{d:"M17 4.978c-1.838 0-2.876.396-3.68.934.513-1.172 1.768-2.934 4.68-2.934a1 1 0 0 0 0-2c-2.921 0-4.629 1.365-5.547 2.512-.064.078-.119.162-.18.244C11.73 1.838 10.798.023 9.207.023 8.579.022 7.85.306 7 .978 5.027 2.54 5.329 3.902 6.492 4.999 3.609 5.222 0 7.352 0 12.969c0 4.582 4.961 11.009 9 11.009 1.975 0 2.371-.486 3-1 .629.514 1.025 1 3 1 4.039 0 9-6.418 9-11 0-5.953-4.055-8-7-8M8.242 2.546c.641-.508.943-.523.965-.523.426.169.975 1.405 1.357 3.055-1.527-.629-2.741-1.352-2.98-1.846.059-.112.241-.356.658-.686M15 21.978c-1.08 0-1.21-.109-1.559-.402l-.176-.146c-.367-.302-.816-.452-1.266-.452s-.898.15-1.266.452l-.176.146c-.347.292-.477.402-1.557.402-2.813 0-7-5.389-7-9.009 0-5.823 4.488-5.991 5-5.991 1.939 0 2.484.471 3.387 1.251l.323.276a1.995 1.995 0 0 0 2.58 0l.323-.276c.902-.78 1.447-1.251 3.387-1.251.512 0 5 .168 5 6 0 3.617-4.187 9-7 9"})}),solid:rI("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",children:rI("path",{d:"M481.9 270.1C490.9 279.1 496 291.3 496 304C496 316.7 490.9 328.9 481.9 337.9C472.9 346.9 460.7 352 448 352H64C51.27 352 39.06 346.9 30.06 337.9C21.06 328.9 16 316.7 16 304C16 291.3 21.06 279.1 30.06 270.1C39.06 261.1 51.27 256 64 256H448C460.7 256 472.9 261.1 481.9 270.1zM475.3 388.7C478.3 391.7 480 395.8 480 400V416C480 432.1 473.3 449.3 461.3 461.3C449.3 473.3 432.1 480 416 480H96C79.03 480 62.75 473.3 50.75 461.3C38.74 449.3 32 432.1 32 416V400C32 395.8 33.69 391.7 36.69 388.7C39.69 385.7 43.76 384 48 384H464C468.2 384 472.3 385.7 475.3 388.7zM50.39 220.8C45.93 218.6 42.03 215.5 38.97 211.6C35.91 207.7 33.79 203.2 32.75 198.4C31.71 193.5 31.8 188.5 32.99 183.7C54.98 97.02 146.5 32 256 32C365.5 32 457 97.02 479 183.7C480.2 188.5 480.3 193.5 479.2 198.4C478.2 203.2 476.1 207.7 473 211.6C469.1 215.5 466.1 218.6 461.6 220.8C457.2 222.9 452.3 224 447.3 224H64.67C59.73 224 54.84 222.9 50.39 220.8zM372.7 116.7C369.7 119.7 368 123.8 368 128C368 131.2 368.9 134.3 370.7 136.9C372.5 139.5 374.1 141.6 377.9 142.8C380.8 143.1 384 144.3 387.1 143.7C390.2 143.1 393.1 141.6 395.3 139.3C397.6 137.1 399.1 134.2 399.7 131.1C400.3 128 399.1 124.8 398.8 121.9C397.6 118.1 395.5 116.5 392.9 114.7C390.3 112.9 387.2 111.1 384 111.1C379.8 111.1 375.7 113.7 372.7 116.7V116.7zM244.7 84.69C241.7 87.69 240 91.76 240 96C240 99.16 240.9 102.3 242.7 104.9C244.5 107.5 246.1 109.6 249.9 110.8C252.8 111.1 256 112.3 259.1 111.7C262.2 111.1 265.1 109.6 267.3 107.3C269.6 105.1 271.1 102.2 271.7 99.12C272.3 96.02 271.1 92.8 270.8 89.88C269.6 86.95 267.5 84.45 264.9 82.7C262.3 80.94 259.2 79.1 256 79.1C251.8 79.1 247.7 81.69 244.7 84.69V84.69zM116.7 116.7C113.7 119.7 112 123.8 112 128C112 131.2 112.9 134.3 114.7 136.9C116.5 139.5 118.1 141.6 121.9 142.8C124.8 143.1 128 144.3 131.1 143.7C134.2 143.1 137.1 141.6 139.3 139.3C141.6 137.1 143.1 134.2 143.7 131.1C144.3 128 143.1 124.8 142.8 121.9C141.6 118.1 139.5 116.5 136.9 114.7C134.3 112.9 131.2 111.1 128 111.1C123.8 111.1 119.7 113.7 116.7 116.7L116.7 116.7z"})})},frequent:{outline:rI("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[rI("path",{d:"M13 4h-2l-.001 7H9v2h2v2h2v-2h4v-2h-4z"}),rI("path",{d:"M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0m0 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10"})]}),solid:rI("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",children:rI("path",{d:"M256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256C512 397.4 397.4 512 256 512zM232 256C232 264 236 271.5 242.7 275.1L338.7 339.1C349.7 347.3 364.6 344.3 371.1 333.3C379.3 322.3 376.3 307.4 365.3 300L280 243.2V120C280 106.7 269.3 96 255.1 96C242.7 96 231.1 106.7 231.1 120L232 256z"})})},nature:{outline:rI("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[rI("path",{d:"M15.5 8a1.5 1.5 0 1 0 .001 3.001A1.5 1.5 0 0 0 15.5 8M8.5 8a1.5 1.5 0 1 0 .001 3.001A1.5 1.5 0 0 0 8.5 8"}),rI("path",{d:"M18.933 0h-.027c-.97 0-2.138.787-3.018 1.497-1.274-.374-2.612-.51-3.887-.51-1.285 0-2.616.133-3.874.517C7.245.79 6.069 0 5.093 0h-.027C3.352 0 .07 2.67.002 7.026c-.039 2.479.276 4.238 1.04 5.013.254.258.882.677 1.295.882.191 3.177.922 5.238 2.536 6.38.897.637 2.187.949 3.2 1.102C8.04 20.6 8 20.795 8 21c0 1.773 2.35 3 4 3 1.648 0 4-1.227 4-3 0-.201-.038-.393-.072-.586 2.573-.385 5.435-1.877 5.925-7.587.396-.22.887-.568 1.104-.788.763-.774 1.079-2.534 1.04-5.013C23.929 2.67 20.646 0 18.933 0M3.223 9.135c-.237.281-.837 1.155-.884 1.238-.15-.41-.368-1.349-.337-3.291.051-3.281 2.478-4.972 3.091-5.031.256.015.731.27 1.265.646-1.11 1.171-2.275 2.915-2.352 5.125-.133.546-.398.858-.783 1.313M12 22c-.901 0-1.954-.693-2-1 0-.654.475-1.236 1-1.602V20a1 1 0 1 0 2 0v-.602c.524.365 1 .947 1 1.602-.046.307-1.099 1-2 1m3-3.48v.02a4.752 4.752 0 0 0-1.262-1.02c1.092-.516 2.239-1.334 2.239-2.217 0-1.842-1.781-2.195-3.977-2.195-2.196 0-3.978.354-3.978 2.195 0 .883 1.148 1.701 2.238 2.217A4.8 4.8 0 0 0 9 18.539v-.025c-1-.076-2.182-.281-2.973-.842-1.301-.92-1.838-3.045-1.853-6.478l.023-.041c.496-.826 1.49-1.45 1.804-3.102 0-2.047 1.357-3.631 2.362-4.522C9.37 3.178 10.555 3 11.948 3c1.447 0 2.685.192 3.733.57 1 .9 2.316 2.465 2.316 4.48.313 1.651 1.307 2.275 1.803 3.102.035.058.068.117.102.178-.059 5.967-1.949 7.01-4.902 7.19m6.628-8.202c-.037-.065-.074-.13-.113-.195a7.587 7.587 0 0 0-.739-.987c-.385-.455-.648-.768-.782-1.313-.076-2.209-1.241-3.954-2.353-5.124.531-.376 1.004-.63 1.261-.647.636.071 3.044 1.764 3.096 5.031.027 1.81-.347 3.218-.37 3.235"})]}),solid:rI("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 576 512",children:rI("path",{d:"M332.7 19.85C334.6 8.395 344.5 0 356.1 0C363.6 0 370.6 3.52 375.1 9.502L392 32H444.1C456.8 32 469.1 37.06 478.1 46.06L496 64H552C565.3 64 576 74.75 576 88V112C576 156.2 540.2 192 496 192H426.7L421.6 222.5L309.6 158.5L332.7 19.85zM448 64C439.2 64 432 71.16 432 80C432 88.84 439.2 96 448 96C456.8 96 464 88.84 464 80C464 71.16 456.8 64 448 64zM416 256.1V480C416 497.7 401.7 512 384 512H352C334.3 512 320 497.7 320 480V364.8C295.1 377.1 268.8 384 240 384C211.2 384 184 377.1 160 364.8V480C160 497.7 145.7 512 128 512H96C78.33 512 64 497.7 64 480V249.8C35.23 238.9 12.64 214.5 4.836 183.3L.9558 167.8C-3.331 150.6 7.094 133.2 24.24 128.1C41.38 124.7 58.76 135.1 63.05 152.2L66.93 167.8C70.49 182 83.29 191.1 97.97 191.1H303.8L416 256.1z"})})},objects:{outline:rI("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[rI("path",{d:"M12 0a9 9 0 0 0-5 16.482V21s2.035 3 5 3 5-3 5-3v-4.518A9 9 0 0 0 12 0zm0 2c3.86 0 7 3.141 7 7s-3.14 7-7 7-7-3.141-7-7 3.14-7 7-7zM9 17.477c.94.332 1.946.523 3 .523s2.06-.19 3-.523v.834c-.91.436-1.925.689-3 .689a6.924 6.924 0 0 1-3-.69v-.833zm.236 3.07A8.854 8.854 0 0 0 12 21c.965 0 1.888-.167 2.758-.451C14.155 21.173 13.153 22 12 22c-1.102 0-2.117-.789-2.764-1.453z"}),rI("path",{d:"M14.745 12.449h-.004c-.852-.024-1.188-.858-1.577-1.824-.421-1.061-.703-1.561-1.182-1.566h-.009c-.481 0-.783.497-1.235 1.537-.436.982-.801 1.811-1.636 1.791l-.276-.043c-.565-.171-.853-.691-1.284-1.794-.125-.313-.202-.632-.27-.913-.051-.213-.127-.53-.195-.634C7.067 9.004 7.039 9 6.99 9A1 1 0 0 1 7 7h.01c1.662.017 2.015 1.373 2.198 2.134.486-.981 1.304-2.058 2.797-2.075 1.531.018 2.28 1.153 2.731 2.141l.002-.008C14.944 8.424 15.327 7 16.979 7h.032A1 1 0 1 1 17 9h-.011c-.149.076-.256.474-.319.709a6.484 6.484 0 0 1-.311.951c-.429.973-.79 1.789-1.614 1.789"})]}),solid:rI("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 384 512",children:rI("path",{d:"M112.1 454.3c0 6.297 1.816 12.44 5.284 17.69l17.14 25.69c5.25 7.875 17.17 14.28 26.64 14.28h61.67c9.438 0 21.36-6.401 26.61-14.28l17.08-25.68c2.938-4.438 5.348-12.37 5.348-17.7L272 415.1h-160L112.1 454.3zM191.4 .0132C89.44 .3257 16 82.97 16 175.1c0 44.38 16.44 84.84 43.56 115.8c16.53 18.84 42.34 58.23 52.22 91.45c.0313 .25 .0938 .5166 .125 .7823h160.2c.0313-.2656 .0938-.5166 .125-.7823c9.875-33.22 35.69-72.61 52.22-91.45C351.6 260.8 368 220.4 368 175.1C368 78.61 288.9-.2837 191.4 .0132zM192 96.01c-44.13 0-80 35.89-80 79.1C112 184.8 104.8 192 96 192S80 184.8 80 176c0-61.76 50.25-111.1 112-111.1c8.844 0 16 7.159 16 16S200.8 96.01 192 96.01z"})})},people:{outline:rI("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[rI("path",{d:"M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0m0 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10"}),rI("path",{d:"M8 7a2 2 0 1 0-.001 3.999A2 2 0 0 0 8 7M16 7a2 2 0 1 0-.001 3.999A2 2 0 0 0 16 7M15.232 15c-.693 1.195-1.87 2-3.349 2-1.477 0-2.655-.805-3.347-2H15m3-2H6a6 6 0 1 0 12 0"})]}),solid:rI("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",children:rI("path",{d:"M0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256zM256 432C332.1 432 396.2 382 415.2 314.1C419.1 300.4 407.8 288 393.6 288H118.4C104.2 288 92.92 300.4 96.76 314.1C115.8 382 179.9 432 256 432V432zM176.4 160C158.7 160 144.4 174.3 144.4 192C144.4 209.7 158.7 224 176.4 224C194 224 208.4 209.7 208.4 192C208.4 174.3 194 160 176.4 160zM336.4 224C354 224 368.4 209.7 368.4 192C368.4 174.3 354 160 336.4 160C318.7 160 304.4 174.3 304.4 192C304.4 209.7 318.7 224 336.4 224z"})})},places:{outline:rI("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[rI("path",{d:"M6.5 12C5.122 12 4 13.121 4 14.5S5.122 17 6.5 17 9 15.879 9 14.5 7.878 12 6.5 12m0 3c-.275 0-.5-.225-.5-.5s.225-.5.5-.5.5.225.5.5-.225.5-.5.5M17.5 12c-1.378 0-2.5 1.121-2.5 2.5s1.122 2.5 2.5 2.5 2.5-1.121 2.5-2.5-1.122-2.5-2.5-2.5m0 3c-.275 0-.5-.225-.5-.5s.225-.5.5-.5.5.225.5.5-.225.5-.5.5"}),rI("path",{d:"M22.482 9.494l-1.039-.346L21.4 9h.6c.552 0 1-.439 1-.992 0-.006-.003-.008-.003-.008H23c0-1-.889-2-1.984-2h-.642l-.731-1.717C19.262 3.012 18.091 2 16.764 2H7.236C5.909 2 4.738 3.012 4.357 4.283L3.626 6h-.642C1.889 6 1 7 1 8h.003S1 8.002 1 8.008C1 8.561 1.448 9 2 9h.6l-.043.148-1.039.346a2.001 2.001 0 0 0-1.359 2.097l.751 7.508a1 1 0 0 0 .994.901H3v1c0 1.103.896 2 2 2h2c1.104 0 2-.897 2-2v-1h6v1c0 1.103.896 2 2 2h2c1.104 0 2-.897 2-2v-1h1.096a.999.999 0 0 0 .994-.901l.751-7.508a2.001 2.001 0 0 0-1.359-2.097M6.273 4.857C6.402 4.43 6.788 4 7.236 4h9.527c.448 0 .834.43.963.857L19.313 9H4.688l1.585-4.143zM7 21H5v-1h2v1zm12 0h-2v-1h2v1zm2.189-3H2.811l-.662-6.607L3 11h18l.852.393L21.189 18z"})]}),solid:rI("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",children:rI("path",{d:"M39.61 196.8L74.8 96.29C88.27 57.78 124.6 32 165.4 32H346.6C387.4 32 423.7 57.78 437.2 96.29L472.4 196.8C495.6 206.4 512 229.3 512 256V448C512 465.7 497.7 480 480 480H448C430.3 480 416 465.7 416 448V400H96V448C96 465.7 81.67 480 64 480H32C14.33 480 0 465.7 0 448V256C0 229.3 16.36 206.4 39.61 196.8V196.8zM109.1 192H402.9L376.8 117.4C372.3 104.6 360.2 96 346.6 96H165.4C151.8 96 139.7 104.6 135.2 117.4L109.1 192zM96 256C78.33 256 64 270.3 64 288C64 305.7 78.33 320 96 320C113.7 320 128 305.7 128 288C128 270.3 113.7 256 96 256zM416 320C433.7 320 448 305.7 448 288C448 270.3 433.7 256 416 256C398.3 256 384 270.3 384 288C384 305.7 398.3 320 416 320z"})})},symbols:{outline:rI("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:rI("path",{d:"M0 0h11v2H0zM4 11h3V6h4V4H0v2h4zM15.5 17c1.381 0 2.5-1.116 2.5-2.493s-1.119-2.493-2.5-2.493S13 13.13 13 14.507 14.119 17 15.5 17m0-2.986c.276 0 .5.222.5.493 0 .272-.224.493-.5.493s-.5-.221-.5-.493.224-.493.5-.493M21.5 19.014c-1.381 0-2.5 1.116-2.5 2.493S20.119 24 21.5 24s2.5-1.116 2.5-2.493-1.119-2.493-2.5-2.493m0 2.986a.497.497 0 0 1-.5-.493c0-.271.224-.493.5-.493s.5.222.5.493a.497.497 0 0 1-.5.493M22 13l-9 9 1.513 1.5 8.99-9.009zM17 11c2.209 0 4-1.119 4-2.5V2s.985-.161 1.498.949C23.01 4.055 23 6 23 6s1-1.119 1-3.135C24-.02 21 0 21 0h-2v6.347A5.853 5.853 0 0 0 17 6c-2.209 0-4 1.119-4 2.5s1.791 2.5 4 2.5M10.297 20.482l-1.475-1.585a47.54 47.54 0 0 1-1.442 1.129c-.307-.288-.989-1.016-2.045-2.183.902-.836 1.479-1.466 1.729-1.892s.376-.871.376-1.336c0-.592-.273-1.178-.818-1.759-.546-.581-1.329-.871-2.349-.871-1.008 0-1.79.293-2.344.879-.556.587-.832 1.181-.832 1.784 0 .813.419 1.748 1.256 2.805-.847.614-1.444 1.208-1.794 1.784a3.465 3.465 0 0 0-.523 1.833c0 .857.308 1.56.924 2.107.616.549 1.423.823 2.42.823 1.173 0 2.444-.379 3.813-1.137L8.235 24h2.819l-2.09-2.383 1.333-1.135zm-6.736-6.389a1.02 1.02 0 0 1 .73-.286c.31 0 .559.085.747.254a.849.849 0 0 1 .283.659c0 .518-.419 1.112-1.257 1.784-.536-.651-.805-1.231-.805-1.742a.901.901 0 0 1 .302-.669M3.74 22c-.427 0-.778-.116-1.057-.349-.279-.232-.418-.487-.418-.766 0-.594.509-1.288 1.527-2.083.968 1.134 1.717 1.946 2.248 2.438-.921.507-1.686.76-2.3.76"})}),solid:rI("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",children:rI("path",{d:"M500.3 7.251C507.7 13.33 512 22.41 512 31.1V175.1C512 202.5 483.3 223.1 447.1 223.1C412.7 223.1 383.1 202.5 383.1 175.1C383.1 149.5 412.7 127.1 447.1 127.1V71.03L351.1 90.23V207.1C351.1 234.5 323.3 255.1 287.1 255.1C252.7 255.1 223.1 234.5 223.1 207.1C223.1 181.5 252.7 159.1 287.1 159.1V63.1C287.1 48.74 298.8 35.61 313.7 32.62L473.7 .6198C483.1-1.261 492.9 1.173 500.3 7.251H500.3zM74.66 303.1L86.5 286.2C92.43 277.3 102.4 271.1 113.1 271.1H174.9C185.6 271.1 195.6 277.3 201.5 286.2L213.3 303.1H239.1C266.5 303.1 287.1 325.5 287.1 351.1V463.1C287.1 490.5 266.5 511.1 239.1 511.1H47.1C21.49 511.1-.0019 490.5-.0019 463.1V351.1C-.0019 325.5 21.49 303.1 47.1 303.1H74.66zM143.1 359.1C117.5 359.1 95.1 381.5 95.1 407.1C95.1 434.5 117.5 455.1 143.1 455.1C170.5 455.1 191.1 434.5 191.1 407.1C191.1 381.5 170.5 359.1 143.1 359.1zM440.3 367.1H496C502.7 367.1 508.6 372.1 510.1 378.4C513.3 384.6 511.6 391.7 506.5 396L378.5 508C372.9 512.1 364.6 513.3 358.6 508.9C352.6 504.6 350.3 496.6 353.3 489.7L391.7 399.1H336C329.3 399.1 323.4 395.9 321 389.6C318.7 383.4 320.4 376.3 325.5 371.1L453.5 259.1C459.1 255 467.4 254.7 473.4 259.1C479.4 263.4 481.6 271.4 478.7 278.3L440.3 367.1zM116.7 219.1L19.85 119.2C-8.112 90.26-6.614 42.31 24.85 15.34C51.82-8.137 93.26-3.642 118.2 21.83L128.2 32.32L137.7 21.83C162.7-3.642 203.6-8.137 231.6 15.34C262.6 42.31 264.1 90.26 236.1 119.2L139.7 219.1C133.2 225.6 122.7 225.6 116.7 219.1H116.7z"})})}},search:{loupe:rI("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",children:rI("path",{d:"M12.9 14.32a8 8 0 1 1 1.41-1.41l5.35 5.33-1.42 1.42-5.33-5.34zM8 14A6 6 0 1 0 8 2a6 6 0 0 0 0 12z"})}),delete:rI("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",children:rI("path",{d:"M10 8.586L2.929 1.515 1.515 2.929 8.586 10l-7.071 7.071 1.414 1.414L10 11.414l7.071 7.071 1.414-1.414L11.414 10l7.071-7.071-1.414-1.414L10 8.586z"})})}};function r7(e){let{id:t,skin:n,emoji:r}=e;if(e.shortcodes){let r=e.shortcodes.match(r3.SHORTCODES_REGEX);r&&(t=r[1],r[2]&&(n=r[2]))}if(r||(r=r3.get(t||e.native)),!r)return e.fallback;let i=r.skins[n-1]||r.skins[0],o=i.src||("native"==e.set||e.spritesheet?void 0:"function"==typeof e.getImageURL?e.getImageURL(e.set,i.unified):`https://cdn.jsdelivr.net/npm/emoji-datasource-${e.set}@15.0.1/img/${e.set}/64/${i.unified}.png`),l="function"==typeof e.getSpritesheetURL?e.getSpritesheetURL(e.set):`https://cdn.jsdelivr.net/npm/emoji-datasource-${e.set}@15.0.1/img/${e.set}/sheets-256/64.png`;return rI("span",{class:"emoji-mart-emoji","data-emoji-set":e.set,children:o?rI("img",{style:{maxWidth:e.size||"1em",maxHeight:e.size||"1em",display:"inline-block"},alt:i.native||i.shortcodes,src:o}):"native"==e.set?rI("span",{style:{fontSize:e.size,fontFamily:'"EmojiMart", "Segoe UI Emoji", "Segoe UI Symbol", "Segoe UI", "Apple Color Emoji", "Twemoji Mozilla", "Noto Color Emoji", "Android Emoji"'},children:i.native}):rI("span",{style:{display:"block",width:e.size,height:e.size,backgroundImage:`url(${l})`,backgroundSize:`${100*r$.sheet.cols}% ${100*r$.sheet.rows}%`,backgroundPosition:`${100/(r$.sheet.cols-1)*i.x}% ${100/(r$.sheet.rows-1)*i.y}%`}})})}let r9="undefined"!=typeof window&&window.HTMLElement?window.HTMLElement:Object;class r8 extends r9{static get observedAttributes(){return Object.keys(this.Props)}update(e={}){for(let t in e)this.attributeChangedCallback(t,null,e[t])}attributeChangedCallback(e,t,n){if(!this.component)return;let r=r0(e,{[e]:n},this.constructor.Props,this);this.component.componentWillReceiveProps?this.component.componentWillReceiveProps({[e]:r}):(this.component.props[e]=r,this.component.forceUpdate())}disconnectedCallback(){this.disconnected=!0,this.component&&this.component.unregister&&this.component.unregister()}constructor(e={}){if(super(),this.props=e,e.parent||e.ref){let t=null,n=e.parent||(t=e.ref&&e.ref.current);t&&(t.innerHTML=""),n&&n.appendChild(this)}}}class ie extends r8{setShadow(){this.attachShadow({mode:"open"})}injectStyles(e){if(!e)return;let t=document.createElement("style");t.textContent=e,this.shadowRoot.insertBefore(t,this.shadowRoot.firstChild)}constructor(e,{styles:t}={}){super(e),this.setShadow(),this.injectStyles(t)}}var it={fallback:"",id:"",native:"",shortcodes:"",size:{value:"",transform:e=>/\D/.test(e)?e:`${e}px`},set:rU.set,skin:rU.skin};class ir extends r8{async connectedCallback(){let e=r1(this.props,it,this);e.element=this,e.ref=e=>{this.component=e},await rQ(),this.disconnected||rN(rI(r7,{...e}),this)}constructor(e){super(e)}}re(ir,"Props",it),"undefined"==typeof customElements||customElements.get("em-emoji")||customElements.define("em-emoji",ir);var ii,io,il=0,ia=[],is=rn.__b,ic=rn.__r,iu=rn.diffed,id=rn.__c,ip=rn.unmount;function ih(){var e;for(ia.sort(function(e,t){return e.__v.__b-t.__v.__b});e=ia.pop();)if(e.__P)try{e.__H.__h.forEach(ig),e.__H.__h.forEach(iv),e.__H.__h=[]}catch(t){e.__H.__h=[],rn.__e(t,e.__v)}}rn.__b=function(e){ii=null,is&&is(e)},rn.__r=function(e){ic&&ic(e);var t=(ii=e.__c).__H;t&&(t.__h.forEach(ig),t.__h.forEach(iv),t.__h=[])},rn.diffed=function(e){iu&&iu(e);var t=e.__c;t&&t.__H&&t.__H.__h.length&&(1!==ia.push(t)&&io===rn.requestAnimationFrame||((io=rn.requestAnimationFrame)||function(e){var t,n=function(){clearTimeout(r),im&&cancelAnimationFrame(t),setTimeout(e)},r=setTimeout(n,100);im&&(t=requestAnimationFrame(n))})(ih)),ii=null},rn.__c=function(e,t){t.some(function(e){try{e.__h.forEach(ig),e.__h=e.__h.filter(function(e){return!e.__||iv(e)})}catch(n){t.some(function(e){e.__h&&(e.__h=[])}),t=[],rn.__e(n,e.__v)}}),id&&id(e,t)},rn.unmount=function(e){ip&&ip(e);var t,n=e.__c;n&&n.__H&&(n.__H.__.forEach(function(e){try{ig(e)}catch(e){t=e}}),t&&rn.__e(t,n.__v))};var im="function"==typeof requestAnimationFrame;function ig(e){var t=ii,n=e.__c;"function"==typeof n&&(e.__c=void 0,n()),ii=t}function iv(e){var t=ii;e.__c=e.__(),ii=t}function iy(e,t){for(var n in e)if("__source"!==n&&!(n in t))return!0;for(var r in t)if("__source"!==r&&e[r]!==t[r])return!0;return!1}function ib(e){this.props=e}(ib.prototype=new rg).isPureReactComponent=!0,ib.prototype.shouldComponentUpdate=function(e,t){return iy(this.props,e)||iy(this.state,t)};var ix=rn.__b;rn.__b=function(e){e.type&&e.type.__f&&e.ref&&(e.props.ref=e.ref,e.ref=null),ix&&ix(e)},"undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.forward_ref");var ik=rn.__e;rn.__e=function(e,t,n){if(e.then){for(var r,i=t;i=i.__;)if((r=i.__c)&&r.__c)return null==t.__e&&(t.__e=n.__e,t.__k=n.__k),r.__c(e,t)}ik(e,t,n)};var iw=rn.unmount;function i_(){this.__u=0,this.t=null,this.__b=null}function iC(e){var t=e.__.__c;return t&&t.__e&&t.__e(e)}function iS(){this.u=null,this.o=null}rn.unmount=function(e){var t=e.__c;t&&t.__R&&t.__R(),t&&!0===e.__h&&(e.type=null),iw&&iw(e)},(i_.prototype=new rg).__c=function(e,t){var n=t.__c,r=this;null==r.t&&(r.t=[]),r.t.push(n);var i=iC(r.__v),o=!1,l=function(){o||(o=!0,n.__R=null,i?i(a):a())};n.__R=l;var a=function(){if(!--r.__u){if(r.state.__e){var e,t=r.state.__e;r.__v.__k[0]=function e(t,n,r){return t&&(t.__v=null,t.__k=t.__k&&t.__k.map(function(t){return e(t,n,r)}),t.__c&&t.__c.__P===n&&(t.__e&&r.insertBefore(t.__e,t.__d),t.__c.__e=!0,t.__c.__P=r)),t}(t,t.__c.__P,t.__c.__O)}for(r.setState({__e:r.__b=null});e=r.t.pop();)e.forceUpdate()}},s=!0===t.__h;r.__u++||s||r.setState({__e:r.__b=r.__v.__k[0]}),e.then(l,l)},i_.prototype.componentWillUnmount=function(){this.t=[]},i_.prototype.render=function(e,t){if(this.__b){if(this.__v.__k){var n=document.createElement("div"),r=this.__v.__k[0].__c;this.__v.__k[0]=function e(t,n,r){return t&&(t.__c&&t.__c.__H&&(t.__c.__H.__.forEach(function(e){"function"==typeof e.__c&&e.__c()}),t.__c.__H=null),null!=(t=function(e,t){for(var n in t)e[n]=t[n];return e}({},t)).__c&&(t.__c.__P===r&&(t.__c.__P=n),t.__c=null),t.__k=t.__k&&t.__k.map(function(t){return e(t,n,r)})),t}(this.__b,n,r.__O=r.__P)}this.__b=null}var i=t.__e&&rp(rm,null,e.fallback);return i&&(i.__h=null),[rp(rm,null,t.__e?null:e.children),i]};var ij=function(e,t,n){if(++n[1]===n[0]&&e.o.delete(t),e.props.revealOrder&&("t"!==e.props.revealOrder[0]||!e.o.size))for(n=e.u;n;){for(;n.length>3;)n.pop()();if(n[1]<n[0])break;e.u=n=n[2]}};(iS.prototype=new rg).__e=function(e){var t=this,n=iC(t.__v),r=t.o.get(e);return r[0]++,function(i){var o=function(){t.props.revealOrder?(r.push(i),ij(t,e,r)):i()};n?n(o):o()}},iS.prototype.render=function(e){this.u=null,this.o=new Map;var t=rk(e.children);e.revealOrder&&"b"===e.revealOrder[0]&&t.reverse();for(var n=t.length;n--;)this.o.set(t[n],this.u=[1,0,this.u]);return e.children},iS.prototype.componentDidUpdate=iS.prototype.componentDidMount=function(){var e=this;this.o.forEach(function(t,n){ij(e,n,t)})};var iE="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,iD=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|marker(?!H|W|U)|overline|paint|stop|strikethrough|stroke|text(?!L)|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,iL="undefined"!=typeof document;rg.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach(function(e){Object.defineProperty(rg.prototype,e,{configurable:!0,get:function(){return this["UNSAFE_"+e]},set:function(t){Object.defineProperty(this,e,{configurable:!0,writable:!0,value:t})}})});var iT=rn.event;function iN(){}function iM(){return this.cancelBubble}function iI(){return this.defaultPrevented}rn.event=function(e){return iT&&(e=iT(e)),e.persist=iN,e.isPropagationStopped=iM,e.isDefaultPrevented=iI,e.nativeEvent=e};var iP={configurable:!0,get:function(){return this.class}},iz=rn.vnode;rn.vnode=function(e){var t=e.type,n=e.props,r=n;if("string"==typeof t){var i=-1===t.indexOf("-");for(var o in r={},n){var l,a=n[o];iL&&"children"===o&&"noscript"===t||"value"===o&&"defaultValue"in n&&null==a||("defaultValue"===o&&"value"in n&&null==n.value?o="value":"download"===o&&!0===a?a="":/ondoubleclick/i.test(o)?o="ondblclick":/^onchange(textarea|input)/i.test(o+t)&&(l=n.type,!("undefined"!=typeof Symbol&&"symbol"==typeof Symbol()?/fil|che|rad/i:/fil|che|ra/i).test(l))?o="oninput":/^onfocus$/i.test(o)?o="onfocusin":/^onblur$/i.test(o)?o="onfocusout":/^on(Ani|Tra|Tou|BeforeInp)/.test(o)?o=o.toLowerCase():i&&iD.test(o)?o=o.replace(/[A-Z0-9]/,"-$&").toLowerCase():null===a&&(a=void 0),r[o]=a)}"select"==t&&r.multiple&&Array.isArray(r.value)&&(r.value=rk(n.children).forEach(function(e){e.props.selected=-1!=r.value.indexOf(e.props.value)})),"select"==t&&null!=r.defaultValue&&(r.value=rk(n.children).forEach(function(e){e.props.selected=r.multiple?-1!=r.defaultValue.indexOf(e.props.value):r.defaultValue==e.props.value})),e.props=r,n.class!=n.className&&(iP.enumerable="className"in n,null!=n.className&&(r.class=n.className),Object.defineProperty(r,"className",iP))}e.$$typeof=iE,iz&&iz(e)};var iA=rn.__r;rn.__r=function(e){iA&&iA(e),e.__c};let iq={light:"outline",dark:"solid"};class iR extends ib{renderIcon(e){let{icon:t}=e;if(t){if(t.svg)return rI("span",{class:"flex",dangerouslySetInnerHTML:{__html:t.svg}});if(t.src)return rI("img",{src:t.src})}let n=r6.categories[e.id]||r6.categories.custom;return n["auto"==this.props.icons?iq[this.props.theme]:this.props.icons]||n}render(){let e=null;return rI("nav",{id:"nav",class:"padding","data-position":this.props.position,dir:this.props.dir,children:rI("div",{class:"flex relative",children:[this.categories.map((t,n)=>{let r=t.name||rZ.categories[t.id],i=!this.props.unfocused&&t.id==this.state.categoryId;return i&&(e=n),rI("button",{"aria-label":r,"aria-selected":i||void 0,title:r,type:"button",class:"flex flex-grow flex-center",onMouseDown:e=>e.preventDefault(),onClick:()=>{this.props.onClick({category:t,i:n})},children:this.renderIcon(t)})}),rI("div",{class:"bar",style:{width:`${100/this.categories.length}%`,opacity:null==e?0:1,transform:"rtl"===this.props.dir?`scaleX(-1) translateX(${100*e}%)`:`translateX(${100*e}%)`}})]})})}constructor(){super(),this.categories=r$.categories.filter(e=>!e.target),this.state={categoryId:this.categories[0].id}}}class iO extends ib{shouldComponentUpdate(e){for(let t in e)if("children"!=t&&e[t]!=this.props[t])return!0;return!1}render(){return this.props.children}}let iB={rowsPerRender:10};class iF extends rg{getInitialState(e=this.props){return{skin:rP.get("skin")||e.skin,theme:this.initTheme(e.theme)}}componentWillMount(){this.dir=rZ.rtl?"rtl":"ltr",this.refs={menu:rf(),navigation:rf(),scroll:rf(),search:rf(),searchInput:rf(),skinToneButton:rf(),skinToneRadio:rf()},this.initGrid(),!1==this.props.stickySearch&&"sticky"==this.props.searchPosition&&(console.warn("[EmojiMart] Deprecation warning: `stickySearch` has been renamed `searchPosition`."),this.props.searchPosition="static")}componentDidMount(){if(this.register(),this.shadowRoot=this.base.parentNode,this.props.autoFocus){let{searchInput:e}=this.refs;e.current&&e.current.focus()}}componentWillReceiveProps(e){for(let t in this.nextState||(this.nextState={}),e)this.nextState[t]=e[t];clearTimeout(this.nextStateTimer),this.nextStateTimer=setTimeout(()=>{let e=!1;for(let t in this.nextState)this.props[t]=this.nextState[t],("custom"===t||"categories"===t)&&(e=!0);delete this.nextState;let t=this.getInitialState();if(e)return this.reset(t);this.setState(t)})}componentWillUnmount(){this.unregister()}async reset(e={}){await rQ(this.props),this.initGrid(),this.unobserve(),this.setState(e,()=>{this.observeCategories(),this.observeRows()})}register(){document.addEventListener("click",this.handleClickOutside),this.observe()}unregister(){document.removeEventListener("click",this.handleClickOutside),this.darkMedia?.removeEventListener("change",this.darkMediaCallback),this.unobserve()}observe(){this.observeCategories(),this.observeRows()}unobserve({except:e=[]}={}){for(let t of(Array.isArray(e)||(e=[e]),this.observers))e.includes(t)||t.disconnect();this.observers=[].concat(e)}initGrid(){let{categories:e}=r$;this.refs.categories=new Map;let t=r$.categories.map(e=>e.id).join(",");this.navKey&&this.navKey!=t&&this.refs.scroll.current&&(this.refs.scroll.current.scrollTop=0),this.navKey=t,this.grid=[],this.grid.setsize=0;let n=(e,t)=>{let n=[];n.__categoryId=t.id,n.__index=e.length,this.grid.push(n);let r=this.grid.length-1,i=r%iB.rowsPerRender?{}:rf();return i.index=r,i.posinset=this.grid.setsize+1,e.push(i),n};for(let t of e){let e=[],r=n(e,t);for(let i of t.emojis)r.length==this.getPerLine()&&(r=n(e,t)),this.grid.setsize+=1,r.push(i);this.refs.categories.set(t.id,{root:rf(),rows:e})}}initTheme(e){if("auto"!=e)return e;if(!this.darkMedia){if(this.darkMedia=matchMedia("(prefers-color-scheme: dark)"),this.darkMedia.media.match(/^not/))return"light";this.darkMedia.addEventListener("change",this.darkMediaCallback)}return this.darkMedia.matches?"dark":"light"}initDynamicPerLine(e=this.props){if(!e.dynamicWidth)return;let{element:t,emojiButtonSize:n}=e,r=()=>{let{width:e}=t.getBoundingClientRect();return Math.floor(e/n)},i=new ResizeObserver(()=>{this.unobserve({except:i}),this.setState({perLine:r()},()=>{this.initGrid(),this.forceUpdate(()=>{this.observeCategories(),this.observeRows()})})});return i.observe(t),this.observers.push(i),r()}getPerLine(){return this.state.perLine||this.props.perLine}getEmojiByPos([e,t]){let n=this.state.searchResults||this.grid,r=n[e]&&n[e][t];if(r)return r3.get(r)}observeCategories(){let e=this.refs.navigation.current;if(!e)return;let t=new Map,n=t=>{t!=e.state.categoryId&&e.setState({categoryId:t})},r=new IntersectionObserver(e=>{for(let n of e){let e=n.target.dataset.id;t.set(e,n.intersectionRatio)}for(let[e,r]of[...t])if(r){n(e);break}},{root:this.refs.scroll.current,threshold:[0,1]});for(let{root:e}of this.refs.categories.values())r.observe(e.current);this.observers.push(r)}observeRows(){let e={...this.state.visibleRows},t=new IntersectionObserver(t=>{for(let n of t){let t=parseInt(n.target.dataset.index);n.isIntersecting?e[t]=!0:delete e[t]}this.setState({visibleRows:e})},{root:this.refs.scroll.current,rootMargin:`${this.props.emojiButtonSize*(iB.rowsPerRender+5)}px 0px ${this.props.emojiButtonSize*iB.rowsPerRender}px`});for(let{rows:e}of this.refs.categories.values())for(let n of e)n.current&&t.observe(n.current);this.observers.push(t)}preventDefault(e){e.preventDefault()}unfocusSearch(){let e=this.refs.searchInput.current;e&&e.blur()}navigate({e:e,input:t,left:n,right:r,up:i,down:o}){let l=this.state.searchResults||this.grid;if(!l.length)return;let[a,s]=this.state.pos,c=(()=>{if(0==a&&0==s&&!e.repeat&&(n||i))return null;if(-1==a)return!e.repeat&&(r||o)&&t.selectionStart==t.value.length?[0,0]:null;if(n||r){let e=l[a],t=n?-1:1;if(!e[s+=t]){if(a+=t,!(e=l[a]))return a=n?0:l.length-1,s=n?0:l[a].length-1,[a,s];s=n?e.length-1:0}return[a,s]}if(i||o){let e=l[a+=i?-1:1];return e?e[s]||(s=e.length-1):(a=i?0:l.length-1,s=i?0:l[a].length-1),[a,s]}})();if(c)e.preventDefault();else{this.state.pos[0]>-1&&this.setState({pos:[-1,-1]});return}this.setState({pos:c,keyboard:!0},()=>{this.scrollTo({row:c[0]})})}scrollTo({categoryId:e,row:t}){let n=this.state.searchResults||this.grid;if(!n.length)return;let r=this.refs.scroll.current,i=r.getBoundingClientRect(),o=0;if(t>=0&&(e=n[t].__categoryId),e&&(o=(this.refs[e]||this.refs.categories.get(e).root).current.getBoundingClientRect().top-(i.top-r.scrollTop)+1),t>=0){if(t){let e=o+n[t].__index*this.props.emojiButtonSize,l=e+this.props.emojiButtonSize+.88*this.props.emojiButtonSize;if(e<r.scrollTop)o=e;else{if(!(l>r.scrollTop+i.height))return;o=l-i.height}}else o=0}this.ignoreMouse(),r.scrollTop=o}ignoreMouse(){this.mouseIsIgnored=!0,clearTimeout(this.ignoreMouseTimer),this.ignoreMouseTimer=setTimeout(()=>{delete this.mouseIsIgnored},100)}handleEmojiOver(e){this.mouseIsIgnored||this.state.showSkins||this.setState({pos:e||[-1,-1],keyboard:!1})}handleEmojiClick({e:e,emoji:t,pos:n}){if(this.props.onEmojiSelect&&(!t&&n&&(t=this.getEmojiByPos(n)),t)){let n=function(e,{skinIndex:t=0}={}){let n=e.skins[t]||(t=0,e.skins[t]),r={id:e.id,name:e.name,native:n.native,unified:n.unified,keywords:e.keywords,shortcodes:n.shortcodes||e.shortcodes};return e.skins.length>1&&(r.skin=t+1),n.src&&(r.src=n.src),e.aliases&&e.aliases.length&&(r.aliases=e.aliases),e.emoticons&&e.emoticons.length&&(r.emoticons=e.emoticons),r}(t,{skinIndex:this.state.skin-1});this.props.maxFrequentRows&&rH.add(n,this.props),this.props.onEmojiSelect(n,e)}}closeSkins(){this.state.showSkins&&(this.setState({showSkins:null,tempSkin:null}),this.base.removeEventListener("click",this.handleBaseClick),this.base.removeEventListener("keydown",this.handleBaseKeydown))}handleSkinMouseOver(e){this.setState({tempSkin:e})}handleSkinClick(e){this.ignoreMouse(),this.closeSkins(),this.setState({skin:e,tempSkin:null}),rP.set("skin",e)}renderNav(){return rI(iR,{ref:this.refs.navigation,icons:this.props.icons,theme:this.state.theme,dir:this.dir,unfocused:!!this.state.searchResults,position:this.props.navPosition,onClick:this.handleCategoryClick},this.navKey)}renderPreview(){let e=this.getEmojiByPos(this.state.pos),t=this.state.searchResults&&!this.state.searchResults.length;return rI("div",{id:"preview",class:"flex flex-middle",dir:this.dir,"data-position":this.props.previewPosition,children:[rI("div",{class:"flex flex-middle flex-grow",children:[rI("div",{class:"flex flex-auto flex-middle flex-center",style:{height:this.props.emojiButtonSize,fontSize:this.props.emojiButtonSize},children:rI(r7,{emoji:e,id:t?this.props.noResultsEmoji||"cry":this.props.previewEmoji||("top"==this.props.previewPosition?"point_down":"point_up"),set:this.props.set,size:this.props.emojiButtonSize,skin:this.state.tempSkin||this.state.skin,spritesheet:!0,getSpritesheetURL:this.props.getSpritesheetURL})}),rI("div",{class:`margin-${this.dir[0]}`,children:e||t?rI("div",{class:`padding-${this.dir[2]} align-${this.dir[0]}`,children:[rI("div",{class:"preview-title ellipsis",children:e?e.name:rZ.search_no_results_1}),rI("div",{class:"preview-subtitle ellipsis color-c",children:e?e.skins[0].shortcodes:rZ.search_no_results_2})]}):rI("div",{class:"preview-placeholder color-c",children:rZ.pick})})]}),!e&&"preview"==this.props.skinTonePosition&&this.renderSkinToneButton()]})}renderEmojiButton(e,{pos:t,posinset:n,grid:r}){var i;let o=this.props.emojiButtonSize,l=this.state.tempSkin||this.state.skin,a=(e.skins[l-1]||e.skins[0]).native,s=Array.isArray(i=this.state.pos)&&Array.isArray(t)&&i.length===t.length&&i.every((e,n)=>e==t[n]),c=t.concat(e.id).join("");return rI(iO,{selected:s,skin:l,size:o,children:rI("button",{"aria-label":a,"aria-selected":s||void 0,"aria-posinset":n,"aria-setsize":r.setsize,"data-keyboard":this.state.keyboard,title:"none"==this.props.previewPosition?e.name:void 0,type:"button",class:"flex flex-center flex-middle",tabindex:"-1",onClick:t=>this.handleEmojiClick({e:t,emoji:e}),onMouseEnter:()=>this.handleEmojiOver(t),onMouseLeave:()=>this.handleEmojiOver(),style:{width:this.props.emojiButtonSize,height:this.props.emojiButtonSize,fontSize:this.props.emojiSize,lineHeight:0},children:[rI("div",{"aria-hidden":"true",class:"background",style:{borderRadius:this.props.emojiButtonRadius,backgroundColor:this.props.emojiButtonColors?this.props.emojiButtonColors[(n-1)%this.props.emojiButtonColors.length]:void 0}}),rI(r7,{emoji:e,set:this.props.set,size:this.props.emojiSize,skin:l,spritesheet:!0,getSpritesheetURL:this.props.getSpritesheetURL})]})},c)}renderSearch(){let e="none"==this.props.previewPosition||"search"==this.props.skinTonePosition;return rI("div",{children:[rI("div",{class:"spacer"}),rI("div",{class:"flex flex-middle",children:[rI("div",{class:"search relative flex-grow",children:[rI("input",{type:"search",ref:this.refs.searchInput,placeholder:rZ.search,onClick:this.handleSearchClick,onInput:this.handleSearchInput,onKeyDown:this.handleSearchKeyDown,autoComplete:"off"}),rI("span",{class:"icon loupe flex",children:r6.search.loupe}),this.state.searchResults&&rI("button",{title:"Clear","aria-label":"Clear",type:"button",class:"icon delete flex",onClick:this.clearSearch,onMouseDown:this.preventDefault,children:r6.search.delete})]}),e&&this.renderSkinToneButton()]})]})}renderSearchResults(){let{searchResults:e}=this.state;return e?rI("div",{class:"category",ref:this.refs.search,children:[rI("div",{class:`sticky padding-small align-${this.dir[0]}`,children:rZ.categories.search}),rI("div",{children:e.length?e.map((t,n)=>rI("div",{class:"flex",children:t.map((t,r)=>this.renderEmojiButton(t,{pos:[n,r],posinset:n*this.props.perLine+r+1,grid:e}))})):rI("div",{class:`padding-small align-${this.dir[0]}`,children:this.props.onAddCustomEmoji&&rI("a",{onClick:this.props.onAddCustomEmoji,children:rZ.add_custom})})})]}):null}renderCategories(){let{categories:e}=r$,t=!!this.state.searchResults,n=this.getPerLine();return rI("div",{style:{visibility:t?"hidden":void 0,display:t?"none":void 0,height:"100%"},children:e.map(e=>{let{root:t,rows:r}=this.refs.categories.get(e.id);return rI("div",{"data-id":e.target?e.target.id:e.id,class:"category",ref:t,children:[rI("div",{class:`sticky padding-small align-${this.dir[0]}`,children:e.name||rZ.categories[e.id]}),rI("div",{class:"relative",style:{height:r.length*this.props.emojiButtonSize},children:r.map((t,r)=>{let i=t.index-t.index%iB.rowsPerRender,o=this.state.visibleRows[i],l="current"in t?t:void 0;if(!o&&!l)return null;let a=r*n,s=e.emojis.slice(a,a+n);return s.length<n&&s.push(...Array(n-s.length)),rI("div",{"data-index":t.index,ref:l,class:"flex row",style:{top:r*this.props.emojiButtonSize},children:o&&s.map((e,n)=>{if(!e)return rI("div",{style:{width:this.props.emojiButtonSize,height:this.props.emojiButtonSize}});let r=r3.get(e);return this.renderEmojiButton(r,{pos:[t.index,n],posinset:t.posinset+n,grid:this.grid})})},t.index)})})]})})})}renderSkinToneButton(){return"none"==this.props.skinTonePosition?null:rI("div",{class:"flex flex-auto flex-center flex-middle",style:{position:"relative",width:this.props.emojiButtonSize,height:this.props.emojiButtonSize},children:rI("button",{type:"button",ref:this.refs.skinToneButton,class:"skin-tone-button flex flex-auto flex-center flex-middle","aria-selected":this.state.showSkins?"":void 0,"aria-label":rZ.skins.choose,title:rZ.skins.choose,onClick:this.openSkins,style:{width:this.props.emojiSize,height:this.props.emojiSize},children:rI("span",{class:`skin-tone skin-tone-${this.state.skin}`})})})}renderLiveRegion(){let e=this.getEmojiByPos(this.state.pos);return rI("div",{"aria-live":"polite",class:"sr-only",children:e?e.name:""})}renderSkins(){let e=this.refs.skinToneButton.current.getBoundingClientRect(),t=this.base.getBoundingClientRect(),n={};return"ltr"==this.dir?n.right=t.right-e.right-3:n.left=e.left-t.left-3,"bottom"==this.props.previewPosition&&"preview"==this.props.skinTonePosition?n.bottom=t.bottom-e.top+6:(n.top=e.bottom-t.top+3,n.bottom="auto"),rI("div",{ref:this.refs.menu,role:"radiogroup",dir:this.dir,"aria-label":rZ.skins.choose,class:"menu hidden","data-position":n.top?"top":"bottom",style:n,children:[...Array(6).keys()].map(e=>{let t=e+1,n=this.state.skin==t;return rI("div",{children:[rI("input",{type:"radio",name:"skin-tone",value:t,"aria-label":rZ.skins[t],ref:n?this.refs.skinToneRadio:null,defaultChecked:n,onChange:()=>this.handleSkinMouseOver(t),onKeyDown:e=>{("Enter"==e.code||"Space"==e.code||"Tab"==e.code)&&(e.preventDefault(),this.handleSkinClick(t))}}),rI("button",{"aria-hidden":"true",tabindex:"-1",onClick:()=>this.handleSkinClick(t),onMouseEnter:()=>this.handleSkinMouseOver(t),onMouseLeave:()=>this.handleSkinMouseOver(),class:"option flex flex-grow flex-middle",children:[rI("span",{class:`skin-tone skin-tone-${t}`}),rI("span",{class:"margin-small-lr",children:rZ.skins[t]})]})]})})})}render(){let e=this.props.perLine*this.props.emojiButtonSize;return rI("section",{id:"root",class:"flex flex-column",dir:this.dir,style:{width:this.props.dynamicWidth?"100%":`calc(${e}px + (var(--padding) + var(--sidebar-width)))`},"data-emoji-set":this.props.set,"data-theme":this.state.theme,"data-menu":this.state.showSkins?"":void 0,children:["top"==this.props.previewPosition&&this.renderPreview(),"top"==this.props.navPosition&&this.renderNav(),"sticky"==this.props.searchPosition&&rI("div",{class:"padding-lr",children:this.renderSearch()}),rI("div",{ref:this.refs.scroll,class:"scroll flex-grow padding-lr",children:rI("div",{style:{width:this.props.dynamicWidth?"100%":e,height:"100%"},children:["static"==this.props.searchPosition&&this.renderSearch(),this.renderSearchResults(),this.renderCategories()]})}),"bottom"==this.props.navPosition&&this.renderNav(),"bottom"==this.props.previewPosition&&this.renderPreview(),this.state.showSkins&&this.renderSkins(),this.renderLiveRegion()]})}constructor(e){super(),re(this,"darkMediaCallback",()=>{"auto"==this.props.theme&&this.setState({theme:this.darkMedia.matches?"dark":"light"})}),re(this,"handleClickOutside",e=>{let{element:t}=this.props;e.target!=t&&(this.state.showSkins&&this.closeSkins(),this.props.onClickOutside&&this.props.onClickOutside(e))}),re(this,"handleBaseClick",e=>{this.state.showSkins&&!e.target.closest(".menu")&&(e.preventDefault(),e.stopImmediatePropagation(),this.closeSkins())}),re(this,"handleBaseKeydown",e=>{this.state.showSkins&&"Escape"==e.key&&(e.preventDefault(),e.stopImmediatePropagation(),this.closeSkins())}),re(this,"handleSearchClick",()=>{this.getEmojiByPos(this.state.pos)&&this.setState({pos:[-1,-1]})}),re(this,"handleSearchInput",async()=>{let e=this.refs.searchInput.current;if(!e)return;let{value:t}=e,n=await r3.search(t),r=()=>{this.refs.scroll.current&&(this.refs.scroll.current.scrollTop=0)};if(!n)return this.setState({searchResults:n,pos:[-1,-1]},r);let i=e.selectionStart==e.value.length?[0,0]:[-1,-1],o=[];o.setsize=n.length;let l=null;for(let e of n)o.length&&l.length!=this.getPerLine()||((l=[]).__categoryId="search",l.__index=o.length,o.push(l)),l.push(e);this.ignoreMouse(),this.setState({searchResults:o,pos:i},r)}),re(this,"handleSearchKeyDown",e=>{let t=e.currentTarget;switch(e.stopImmediatePropagation(),e.key){case"ArrowLeft":this.navigate({e:e,input:t,left:!0});break;case"ArrowRight":this.navigate({e:e,input:t,right:!0});break;case"ArrowUp":this.navigate({e:e,input:t,up:!0});break;case"ArrowDown":this.navigate({e:e,input:t,down:!0});break;case"Enter":e.preventDefault(),this.handleEmojiClick({e:e,pos:this.state.pos});break;case"Escape":e.preventDefault(),this.state.searchResults?this.clearSearch():this.unfocusSearch()}}),re(this,"clearSearch",()=>{let e=this.refs.searchInput.current;e&&(e.value="",e.focus(),this.handleSearchInput())}),re(this,"handleCategoryClick",({category:e,i:t})=>{this.scrollTo(0==t?{row:-1}:{categoryId:e.id})}),re(this,"openSkins",e=>{let{currentTarget:t}=e,n=t.getBoundingClientRect();this.setState({showSkins:n},async()=>{await r5(2);let e=this.refs.menu.current;e&&(e.classList.remove("hidden"),this.refs.skinToneRadio.current.focus(),this.base.addEventListener("click",this.handleBaseClick,!0),this.base.addEventListener("keydown",this.handleBaseKeydown,!0))})}),this.observers=[],this.state={pos:[-1,-1],perLine:this.initDynamicPerLine(e),visibleRows:{0:!0},...this.getInitialState(e)}}}class iH extends ie{async connectedCallback(){let e=r1(this.props,rU,this);e.element=this,e.ref=e=>{this.component=e},await rQ(e),this.disconnected||rN(rI(iF,{...e}),this.shadowRoot)}constructor(e){super(e,{styles:n8(iV)})}}re(iH,"Props",rU),"undefined"==typeof customElements||customElements.get("em-emoji-picker")||customElements.define("em-emoji-picker",iH);var iV={};function iU(e){let t=(0,l.useRef)(null),n=(0,l.useRef)(null);return n.current&&n.current.update(e),(0,l.useEffect)(()=>(n.current=new iH({...e,ref:t}),()=>{n.current=null}),[]),a().createElement("div",{ref:t})}iV=':host {\n  width: min-content;\n  height: 435px;\n  min-height: 230px;\n  border-radius: var(--border-radius);\n  box-shadow: var(--shadow);\n  --border-radius: 10px;\n  --category-icon-size: 18px;\n  --font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", sans-serif;\n  --font-size: 15px;\n  --preview-placeholder-size: 21px;\n  --preview-title-size: 1.1em;\n  --preview-subtitle-size: .9em;\n  --shadow-color: 0deg 0% 0%;\n  --shadow: .3px .5px 2.7px hsl(var(--shadow-color) / .14), .4px .8px 1px -3.2px hsl(var(--shadow-color) / .14), 1px 2px 2.5px -4.5px hsl(var(--shadow-color) / .14);\n  display: flex;\n}\n\n[data-theme="light"] {\n  --em-rgb-color: var(--rgb-color, 34, 36, 39);\n  --em-rgb-accent: var(--rgb-accent, 34, 102, 237);\n  --em-rgb-background: var(--rgb-background, 255, 255, 255);\n  --em-rgb-input: var(--rgb-input, 255, 255, 255);\n  --em-color-border: var(--color-border, rgba(0, 0, 0, .05));\n  --em-color-border-over: var(--color-border-over, rgba(0, 0, 0, .1));\n}\n\n[data-theme="dark"] {\n  --em-rgb-color: var(--rgb-color, 222, 222, 221);\n  --em-rgb-accent: var(--rgb-accent, 58, 130, 247);\n  --em-rgb-background: var(--rgb-background, 21, 22, 23);\n  --em-rgb-input: var(--rgb-input, 0, 0, 0);\n  --em-color-border: var(--color-border, rgba(255, 255, 255, .1));\n  --em-color-border-over: var(--color-border-over, rgba(255, 255, 255, .2));\n}\n\n#root {\n  --color-a: rgb(var(--em-rgb-color));\n  --color-b: rgba(var(--em-rgb-color), .65);\n  --color-c: rgba(var(--em-rgb-color), .45);\n  --padding: 12px;\n  --padding-small: calc(var(--padding) / 2);\n  --sidebar-width: 16px;\n  --duration: 225ms;\n  --duration-fast: 125ms;\n  --duration-instant: 50ms;\n  --easing: cubic-bezier(.4, 0, .2, 1);\n  width: 100%;\n  text-align: left;\n  border-radius: var(--border-radius);\n  background-color: rgb(var(--em-rgb-background));\n  position: relative;\n}\n\n@media (prefers-reduced-motion) {\n  #root {\n    --duration: 0;\n    --duration-fast: 0;\n    --duration-instant: 0;\n  }\n}\n\n#root[data-menu] button {\n  cursor: auto;\n}\n\n#root[data-menu] .menu button {\n  cursor: pointer;\n}\n\n:host, #root, input, button {\n  color: rgb(var(--em-rgb-color));\n  font-family: var(--font-family);\n  font-size: var(--font-size);\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  line-height: normal;\n}\n\n*, :before, :after {\n  box-sizing: border-box;\n  min-width: 0;\n  margin: 0;\n  padding: 0;\n}\n\n.relative {\n  position: relative;\n}\n\n.flex {\n  display: flex;\n}\n\n.flex-auto {\n  flex: none;\n}\n\n.flex-center {\n  justify-content: center;\n}\n\n.flex-column {\n  flex-direction: column;\n}\n\n.flex-grow {\n  flex: auto;\n}\n\n.flex-middle {\n  align-items: center;\n}\n\n.flex-wrap {\n  flex-wrap: wrap;\n}\n\n.padding {\n  padding: var(--padding);\n}\n\n.padding-t {\n  padding-top: var(--padding);\n}\n\n.padding-lr {\n  padding-left: var(--padding);\n  padding-right: var(--padding);\n}\n\n.padding-r {\n  padding-right: var(--padding);\n}\n\n.padding-small {\n  padding: var(--padding-small);\n}\n\n.padding-small-b {\n  padding-bottom: var(--padding-small);\n}\n\n.padding-small-lr {\n  padding-left: var(--padding-small);\n  padding-right: var(--padding-small);\n}\n\n.margin {\n  margin: var(--padding);\n}\n\n.margin-r {\n  margin-right: var(--padding);\n}\n\n.margin-l {\n  margin-left: var(--padding);\n}\n\n.margin-small-l {\n  margin-left: var(--padding-small);\n}\n\n.margin-small-lr {\n  margin-left: var(--padding-small);\n  margin-right: var(--padding-small);\n}\n\n.align-l {\n  text-align: left;\n}\n\n.align-r {\n  text-align: right;\n}\n\n.color-a {\n  color: var(--color-a);\n}\n\n.color-b {\n  color: var(--color-b);\n}\n\n.color-c {\n  color: var(--color-c);\n}\n\n.ellipsis {\n  white-space: nowrap;\n  max-width: 100%;\n  width: auto;\n  text-overflow: ellipsis;\n  overflow: hidden;\n}\n\n.sr-only {\n  width: 1px;\n  height: 1px;\n  position: absolute;\n  top: auto;\n  left: -10000px;\n  overflow: hidden;\n}\n\na {\n  cursor: pointer;\n  color: rgb(var(--em-rgb-accent));\n}\n\na:hover {\n  text-decoration: underline;\n}\n\n.spacer {\n  height: 10px;\n}\n\n[dir="rtl"] .scroll {\n  padding-left: 0;\n  padding-right: var(--padding);\n}\n\n.scroll {\n  padding-right: 0;\n  overflow-x: hidden;\n  overflow-y: auto;\n}\n\n.scroll::-webkit-scrollbar {\n  width: var(--sidebar-width);\n  height: var(--sidebar-width);\n}\n\n.scroll::-webkit-scrollbar-track {\n  border: 0;\n}\n\n.scroll::-webkit-scrollbar-button {\n  width: 0;\n  height: 0;\n  display: none;\n}\n\n.scroll::-webkit-scrollbar-corner {\n  background-color: rgba(0, 0, 0, 0);\n}\n\n.scroll::-webkit-scrollbar-thumb {\n  min-height: 20%;\n  min-height: 65px;\n  border: 4px solid rgb(var(--em-rgb-background));\n  border-radius: 8px;\n}\n\n.scroll::-webkit-scrollbar-thumb:hover {\n  background-color: var(--em-color-border-over) !important;\n}\n\n.scroll:hover::-webkit-scrollbar-thumb {\n  background-color: var(--em-color-border);\n}\n\n.sticky {\n  z-index: 1;\n  background-color: rgba(var(--em-rgb-background), .9);\n  -webkit-backdrop-filter: blur(4px);\n  backdrop-filter: blur(4px);\n  font-weight: 500;\n  position: sticky;\n  top: -1px;\n}\n\n[dir="rtl"] .search input[type="search"] {\n  padding: 10px 2.2em 10px 2em;\n}\n\n[dir="rtl"] .search .loupe {\n  left: auto;\n  right: .7em;\n}\n\n[dir="rtl"] .search .delete {\n  left: .7em;\n  right: auto;\n}\n\n.search {\n  z-index: 2;\n  position: relative;\n}\n\n.search input, .search button {\n  font-size: calc(var(--font-size)  - 1px);\n}\n\n.search input[type="search"] {\n  width: 100%;\n  background-color: var(--em-color-border);\n  transition-duration: var(--duration);\n  transition-property: background-color, box-shadow;\n  transition-timing-function: var(--easing);\n  border: 0;\n  border-radius: 10px;\n  outline: 0;\n  padding: 10px 2em 10px 2.2em;\n  display: block;\n}\n\n.search input[type="search"]::-ms-input-placeholder {\n  color: inherit;\n  opacity: .6;\n}\n\n.search input[type="search"]::placeholder {\n  color: inherit;\n  opacity: .6;\n}\n\n.search input[type="search"], .search input[type="search"]::-webkit-search-decoration, .search input[type="search"]::-webkit-search-cancel-button, .search input[type="search"]::-webkit-search-results-button, .search input[type="search"]::-webkit-search-results-decoration {\n  -webkit-appearance: none;\n  -ms-appearance: none;\n  appearance: none;\n}\n\n.search input[type="search"]:focus {\n  background-color: rgb(var(--em-rgb-input));\n  box-shadow: inset 0 0 0 1px rgb(var(--em-rgb-accent)), 0 1px 3px rgba(65, 69, 73, .2);\n}\n\n.search .icon {\n  z-index: 1;\n  color: rgba(var(--em-rgb-color), .7);\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n}\n\n.search .loupe {\n  pointer-events: none;\n  left: .7em;\n}\n\n.search .delete {\n  right: .7em;\n}\n\nsvg {\n  fill: currentColor;\n  width: 1em;\n  height: 1em;\n}\n\nbutton {\n  -webkit-appearance: none;\n  -ms-appearance: none;\n  appearance: none;\n  cursor: pointer;\n  color: currentColor;\n  background-color: rgba(0, 0, 0, 0);\n  border: 0;\n}\n\n#nav {\n  z-index: 2;\n  padding-top: 12px;\n  padding-bottom: 12px;\n  padding-right: var(--sidebar-width);\n  position: relative;\n}\n\n#nav button {\n  color: var(--color-b);\n  transition: color var(--duration) var(--easing);\n}\n\n#nav button:hover {\n  color: var(--color-a);\n}\n\n#nav svg, #nav img {\n  width: var(--category-icon-size);\n  height: var(--category-icon-size);\n}\n\n#nav[dir="rtl"] .bar {\n  left: auto;\n  right: 0;\n}\n\n#nav .bar {\n  width: 100%;\n  height: 3px;\n  background-color: rgb(var(--em-rgb-accent));\n  transition: transform var(--duration) var(--easing);\n  border-radius: 3px 3px 0 0;\n  position: absolute;\n  bottom: -12px;\n  left: 0;\n}\n\n#nav button[aria-selected] {\n  color: rgb(var(--em-rgb-accent));\n}\n\n#preview {\n  z-index: 2;\n  padding: calc(var(--padding)  + 4px) var(--padding);\n  padding-right: var(--sidebar-width);\n  position: relative;\n}\n\n#preview .preview-placeholder {\n  font-size: var(--preview-placeholder-size);\n}\n\n#preview .preview-title {\n  font-size: var(--preview-title-size);\n}\n\n#preview .preview-subtitle {\n  font-size: var(--preview-subtitle-size);\n}\n\n#nav:before, #preview:before {\n  content: "";\n  height: 2px;\n  position: absolute;\n  left: 0;\n  right: 0;\n}\n\n#nav[data-position="top"]:before, #preview[data-position="top"]:before {\n  background: linear-gradient(to bottom, var(--em-color-border), transparent);\n  top: 100%;\n}\n\n#nav[data-position="bottom"]:before, #preview[data-position="bottom"]:before {\n  background: linear-gradient(to top, var(--em-color-border), transparent);\n  bottom: 100%;\n}\n\n.category:last-child {\n  min-height: calc(100% + 1px);\n}\n\n.category button {\n  font-family: -apple-system, BlinkMacSystemFont, Helvetica Neue, sans-serif;\n  position: relative;\n}\n\n.category button > * {\n  position: relative;\n}\n\n.category button .background {\n  opacity: 0;\n  background-color: var(--em-color-border);\n  transition: opacity var(--duration-fast) var(--easing) var(--duration-instant);\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  right: 0;\n}\n\n.category button:hover .background {\n  transition-duration: var(--duration-instant);\n  transition-delay: 0s;\n}\n\n.category button[aria-selected] .background {\n  opacity: 1;\n}\n\n.category button[data-keyboard] .background {\n  transition: none;\n}\n\n.row {\n  width: 100%;\n  position: absolute;\n  top: 0;\n  left: 0;\n}\n\n.skin-tone-button {\n  border: 1px solid rgba(0, 0, 0, 0);\n  border-radius: 100%;\n}\n\n.skin-tone-button:hover {\n  border-color: var(--em-color-border);\n}\n\n.skin-tone-button:active .skin-tone {\n  transform: scale(.85) !important;\n}\n\n.skin-tone-button .skin-tone {\n  transition: transform var(--duration) var(--easing);\n}\n\n.skin-tone-button[aria-selected] {\n  background-color: var(--em-color-border);\n  border-top-color: rgba(0, 0, 0, .05);\n  border-bottom-color: rgba(0, 0, 0, 0);\n  border-left-width: 0;\n  border-right-width: 0;\n}\n\n.skin-tone-button[aria-selected] .skin-tone {\n  transform: scale(.9);\n}\n\n.menu {\n  z-index: 2;\n  white-space: nowrap;\n  border: 1px solid var(--em-color-border);\n  background-color: rgba(var(--em-rgb-background), .9);\n  -webkit-backdrop-filter: blur(4px);\n  backdrop-filter: blur(4px);\n  transition-property: opacity, transform;\n  transition-duration: var(--duration);\n  transition-timing-function: var(--easing);\n  border-radius: 10px;\n  padding: 4px;\n  position: absolute;\n  box-shadow: 1px 1px 5px rgba(0, 0, 0, .05);\n}\n\n.menu.hidden {\n  opacity: 0;\n}\n\n.menu[data-position="bottom"] {\n  transform-origin: 100% 100%;\n}\n\n.menu[data-position="bottom"].hidden {\n  transform: scale(.9)rotate(-3deg)translateY(5%);\n}\n\n.menu[data-position="top"] {\n  transform-origin: 100% 0;\n}\n\n.menu[data-position="top"].hidden {\n  transform: scale(.9)rotate(3deg)translateY(-5%);\n}\n\n.menu input[type="radio"] {\n  clip: rect(0 0 0 0);\n  width: 1px;\n  height: 1px;\n  border: 0;\n  margin: 0;\n  padding: 0;\n  position: absolute;\n  overflow: hidden;\n}\n\n.menu input[type="radio"]:checked + .option {\n  box-shadow: 0 0 0 2px rgb(var(--em-rgb-accent));\n}\n\n.option {\n  width: 100%;\n  border-radius: 6px;\n  padding: 4px 6px;\n}\n\n.option:hover {\n  color: #fff;\n  background-color: rgb(var(--em-rgb-accent));\n}\n\n.skin-tone {\n  width: 16px;\n  height: 16px;\n  border-radius: 100%;\n  display: inline-block;\n  position: relative;\n  overflow: hidden;\n}\n\n.skin-tone:after {\n  content: "";\n  mix-blend-mode: overlay;\n  background: linear-gradient(rgba(255, 255, 255, .2), rgba(0, 0, 0, 0));\n  border: 1px solid rgba(0, 0, 0, .8);\n  border-radius: 100%;\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  box-shadow: inset 0 -2px 3px #000, inset 0 1px 2px #fff;\n}\n\n.skin-tone-1 {\n  background-color: #ffc93a;\n}\n\n.skin-tone-2 {\n  background-color: #ffdab7;\n}\n\n.skin-tone-3 {\n  background-color: #e7b98f;\n}\n\n.skin-tone-4 {\n  background-color: #c88c61;\n}\n\n.skin-tone-5 {\n  background-color: #a46134;\n}\n\n.skin-tone-6 {\n  background-color: #5d4437;\n}\n\n[data-index] {\n  justify-content: space-between;\n}\n\n[data-emoji-set="twitter"] .skin-tone:after {\n  box-shadow: none;\n  border-color: rgba(0, 0, 0, .5);\n}\n\n[data-emoji-set="twitter"] .skin-tone-1 {\n  background-color: #fade72;\n}\n\n[data-emoji-set="twitter"] .skin-tone-2 {\n  background-color: #f3dfd0;\n}\n\n[data-emoji-set="twitter"] .skin-tone-3 {\n  background-color: #eed3a8;\n}\n\n[data-emoji-set="twitter"] .skin-tone-4 {\n  background-color: #cfad8d;\n}\n\n[data-emoji-set="twitter"] .skin-tone-5 {\n  background-color: #a8805d;\n}\n\n[data-emoji-set="twitter"] .skin-tone-6 {\n  background-color: #765542;\n}\n\n[data-emoji-set="google"] .skin-tone:after {\n  box-shadow: inset 0 0 2px 2px rgba(0, 0, 0, .4);\n}\n\n[data-emoji-set="google"] .skin-tone-1 {\n  background-color: #f5c748;\n}\n\n[data-emoji-set="google"] .skin-tone-2 {\n  background-color: #f1d5aa;\n}\n\n[data-emoji-set="google"] .skin-tone-3 {\n  background-color: #d4b48d;\n}\n\n[data-emoji-set="google"] .skin-tone-4 {\n  background-color: #aa876b;\n}\n\n[data-emoji-set="google"] .skin-tone-5 {\n  background-color: #916544;\n}\n\n[data-emoji-set="google"] .skin-tone-6 {\n  background-color: #61493f;\n}\n\n[data-emoji-set="facebook"] .skin-tone:after {\n  border-color: rgba(0, 0, 0, .4);\n  box-shadow: inset 0 -2px 3px #000, inset 0 1px 4px #fff;\n}\n\n[data-emoji-set="facebook"] .skin-tone-1 {\n  background-color: #f5c748;\n}\n\n[data-emoji-set="facebook"] .skin-tone-2 {\n  background-color: #f1d5aa;\n}\n\n[data-emoji-set="facebook"] .skin-tone-3 {\n  background-color: #d4b48d;\n}\n\n[data-emoji-set="facebook"] .skin-tone-4 {\n  background-color: #aa876b;\n}\n\n[data-emoji-set="facebook"] .skin-tone-5 {\n  background-color: #916544;\n}\n\n[data-emoji-set="facebook"] .skin-tone-6 {\n  background-color: #61493f;\n}\n\n';var iZ=n(91664),i$=n(51027);let iG=({onSelect:e})=>{let[t,n]=(0,l.useState)(!1);return(0,o.jsxs)(i$.J2,{open:t,onOpenChange:n,children:[o.jsx(i$.xo,{asChild:!0,children:o.jsx(iZ.z,{className:"my-auto ",variant:"link",size:"sm",children:o.jsx(n9,{className:"h-4 w-4 "})})}),o.jsx(i$.yk,{align:"start",className:"p-0 w-[320px] shadow-md rounded-lg",children:o.jsx(iU,{onEmojiSelect:t=>{e(t.native),n(!1)}})})]})};var iW=n(82631),iK=n(78439),iY=n.n(iK),iJ=n(38443);let iQ=({messageId:e,reactions:t,toggleReaction:n})=>{let r=(0,u.v9)(e=>e.user),i=t=>{n(e,t)};return o.jsx("div",{className:"flex items-center gap-2 mt-2",children:Object.entries(t).map(([e,t])=>o.jsx(iJ.C,{onClick:()=>i(e),className:`cursor-pointer ${Array.isArray(t)&&t.includes(r.uid)?"bg-gray-400":"bg-green"}`,children:(0,o.jsxs)("span",{className:"flex items-center",children:[e," ",t.length>0&&o.jsx("span",{className:"ml-2",children:t.length})," "]})},e))})};iQ.propTypes={messageId:iY().string.isRequired,reactions:iY().any.isRequired,toggleReaction:iY().func.isRequired};var iX=n(31540);function i1({fileName:e,fileUrl:t,fileType:n}){let r=e.length>15?e.substring(14,28)+"...":e;return(0,o.jsxs)("div",{className:"flex items-center space-x-3 p-2 bg-gray-500 rounded-md w-full max-w-md",children:[o.jsx("div",{className:"text-2xl",children:(e=>{switch(e){case"pdf":return"\uD83D\uDCC4";case"ppt":case"pptx":return"\uD83D\uDCCA";case"doc":case"docx":return"\uD83D\uDCDD";default:return"\uD83D\uDCC1"}})(n)}),(0,o.jsxs)("div",{className:"flex-1",children:[o.jsx("p",{className:"text-sm font-medium truncate",children:r}),o.jsx("p",{className:"text-xs  uppercase",children:n})]}),o.jsx(iZ.z,{variant:"ghost",size:"icon",onClick:()=>{let n=document.createElement("a");n.href=t,n.download=e,document.body.appendChild(n),n.click(),document.body.removeChild(n)},title:"Download",children:o.jsx(iX.Z,{className:"h-4 w-4"})})]})}var i0=n(51223),i2=n(3594),i3=n(29752),i4=n(58595),i5=n(6260);function i6({conversation:e}){let[t,n]=(0,l.useState)({userName:"",email:"",profilePic:""}),[r,i]=(0,l.useState)([]),[a,c]=(0,l.useState)(""),[d,p]=(0,l.useState)(!0),[h,C]=(0,l.useState)(!1),S=(0,u.v9)(e=>e.user),j=(0,l.useRef)(null),[E,D]=(0,l.useState)(""),[L,T]=(0,l.useState)(null),N=(0,l.useRef)(null),[M,I]=(0,l.useState)(!1);(0,l.useRef)(r.length);let[P,z]=(0,l.useState)(!1);async function A(e,t,n){try{C(!0);let r=new Date().toISOString();await (0,i4.Hd)("conversations",e?.id,{...t,timestamp:r,replyTo:E||null},r)?(n(""),C(!1)):console.error("Failed to send message")}catch(e){console.error("Error sending message:",e)}finally{C(!1)}}if(!e)return null;async function q(){let t=document.createElement("input");t.type="file",t.onchange=async()=>{let n=t.files?.[0];if(n)try{let t=new FormData;t.append("file",n);let r=(await i5.b.post("/register/upload-image",t,{headers:{"Content-Type":"multipart/form-data"}})).data.data.Location,i={senderId:S.uid,content:r,timestamp:new Date().toISOString()};A(e,i,c)}catch(e){console.error("Error uploading file:",e)}},t.click()}async function R(){try{let t=(await i5.b.post("/meeting",{participants:e.participants})).data.meetLink,n={senderId:S.uid,content:`🔗 Join the Meet: [Click here](${t})`,timestamp:new Date().toISOString()};A(e,n,c)}catch(e){console.error("Error creating meet:",e)}}function O(){if(N.current){let e=N.current,{selectionStart:t,selectionEnd:n,value:r}=e;if(t===n)return;let i=r.slice(t,n);c(`${r.slice(0,t)}**${i}**${r.slice(n)}`),e.setSelectionRange(t+2,n+2)}}let B=()=>{if(N.current){let e=N.current,{selectionStart:t,selectionEnd:n,value:r}=e;if(t===n)return;let i=r.slice(t,n);c(`${r.slice(0,t)}__${i}__${r.slice(n)}`),e.setSelectionRange(t+2,n+2)}};function F(){if(N.current){let e=N.current,{selectionStart:t,selectionEnd:n,value:r}=e;if(t===n)return;let i=r.slice(t,n);c(`${r.slice(0,t)}*${i}*${r.slice(n)}`),e.setSelectionRange(t+1,n+1)}}async function H(t,n){let i=r.find(e=>e.id===t),o={...i?.reactions},l=Object.keys(o).find(e=>o[e]?.includes(S.uid));l===n?(o[n]=o[n].filter(e=>e!==S.uid),0===o[n].length&&delete o[n]):(l&&(o[l]=o[l].filter(e=>e!==S.uid),0===o[l].length&&delete o[l]),o[n]||(o[n]=[]),o[n].push(S.uid)),await (0,i4.fy)(`conversations/${e.id}/messages/`,t,{reactions:o})}return o.jsx(o.Fragment,{children:d?o.jsx("div",{className:"flex justify-center items-center p-5 col-span-3",children:o.jsx(s.Z,{className:"h-6 w-6 text-white animate-spin"})}):(0,o.jsxs)(i3.Zb,{className:"col-span-3 w-[92vw] mt-0 min-h-[70vh] border-gray-400  dark:border-white border-2 shadow-none",children:[o.jsx(i3.Ol,{className:"flex flex-row items-center  bg-[#ececec] dark:bg-[#333333] text-gray-800 dark:text-white p-2 rounded-t-lg",children:(0,o.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,o.jsxs)(i2.Avatar,{children:[o.jsx(i2.AvatarImage,{src:t.profilePic,alt:"Image"}),o.jsx(i2.AvatarFallback,{children:t.userName})]}),(0,o.jsxs)("div",{children:[o.jsx("p",{className:"text-sm font-medium leading-none text-gray-800 dark:text-white",children:t.userName}),o.jsx("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:t.email})]})]})}),o.jsx(i3.aY,{className:"flex-1 px-2 pb-2 pt-2 bg-[#ffffff] dark:bg-[#181818]",children:(0,o.jsxs)("div",{className:"flex flex-col-reverse space-y-4 space-y-reverse overflow-y-auto h-[65vh] md:h-[58vh]",children:[o.jsx("div",{ref:j}),r.map((e,n)=>{let i=function(e){let t=new Date(e);return(0,n2.K)(t,(0,n0.y)(t))?(0,n3.WU)(t,"hh:mm a"):(0,n2.K)(t,(0,n4.k)((0,n0.y)(t),1))?`Yesterday, ${(0,n3.WU)(t,"hh:mm a")}`:(0,n5.F)(t,(0,n0.y)(t))?(0,n3.WU)(t,"MMM dd, hh:mm a"):(0,n3.WU)(t,"yyyy MMM dd, hh:mm a")}(e.timestamp),l=(0,n6.Q)(new Date(e.timestamp))+" ago";return(0,o.jsxs)("div",{id:e.id,className:"flex flex-row relative",onMouseEnter:()=>T(e.id),onMouseLeave:()=>T(null),children:[e.senderId!==S.uid&&(0,o.jsxs)(i2.Avatar,{className:"w-8 h-8 mr-1 my-auto",children:[o.jsx(i2.AvatarImage,{src:t.profilePic,alt:e.senderId}),o.jsx(i2.AvatarFallback,{children:e.senderId.charAt(0).toUpperCase()})]},n),(0,o.jsxs)("div",{className:(0,i0.cn)("flex w-max max-w-[65%] flex-col gap-1 rounded-lg px-3 py-2 text-sm",e.senderId===S.uid?"ml-auto bg-[#9155bc] dark:bg-[#580d8f] text-white  rounded-tr-none":"bg-[#d9d9d9] dark:bg-[#333333] text-white  rounded-tl-none"),onClick:()=>{if(e.replyTo){let t=r.find(t=>t.id===e.replyTo);if(t){let e=document.getElementById(t.id);e&&(e.classList.add("bg-gray-200","dark:bg-gray-600","border-2","border-gray-300","dark:border-gray-500","bg-opacity-50","dark:bg-opacity-50"),e.scrollIntoView({behavior:"smooth"}),setTimeout(()=>{e.classList.remove("bg-gray-200","dark:bg-gray-600","border-2","border-gray-300","dark:border-gray-500","bg-opacity-50","dark:bg-opacity-50")},2e3))}}},children:[o.jsx(iW.TooltipProvider,{children:(0,o.jsxs)(iW.u,{children:[o.jsx(iW.aJ,{asChild:!0,children:(0,o.jsxs)("div",{className:"break-words rounded-lg w-full",children:[e.replyTo&&o.jsx("div",{className:"flex items-center justify-between p-2 bg-gray-200 dark:bg-gray-600 rounded-lg border-l-4 border-gray-400 dark:border-gray-500 shadow-sm opacity-100 transition-opacity duration-300 max-w-2xl mb-1",children:o.jsx("div",{className:"text-sm italic text-gray-600 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 overflow-hidden whitespace-pre-wrap text-ellipsis max-h-[3em] line-clamp-2 max-w-2xl",children:o.jsx("span",{className:"font-semibold",children:r.find(t=>t.id===e.replyTo)?.content||"Message not found"})})}),e.content.match(/\.(jpeg|jpg|gif|png)$/)?o.jsx(n7.default,{src:e.content||"/placeholder.svg",alt:"Message Image",width:300,height:300,className:"rounded-lg"}):e.content.match(/\.(pdf|doc|docx|ppt|pptx)$/)?o.jsx(i1,{fileName:e.content.split("/").pop()||"File",fileUrl:e.content,fileType:e.content.split(".").pop()||"file"}):o.jsx(nX,{className:` ${e.senderId===S.uid?"text-white":"text-black"} dark:text-gray-100`,children:e.content})]})}),o.jsx(iW._v,{side:"bottom",sideOffset:10,children:o.jsx("p",{className:"  p-1 rounded",children:l})})]})}),o.jsx(iQ,{messageId:e.id,reactions:e.reactions||{},toggleReaction:H}),(0,o.jsxs)("div",{className:(0,i0.cn)("text-[10px] mt-1 text-right",e.senderId===S.uid?"text-gray-100 dark:text-gray-300 flex items-center gap-0.5":"text-gray-500 dark:text-gray-400"),children:[i,e.senderId===S.uid&&o.jsx("span",{className:"ml-1",children:o.jsx(f,{className:"w-4"})})]})]}),o.jsx("div",{className:`relative ${e.senderId===S.uid?"text-right":"text-left"}`,children:L===e.id&&o.jsx(m,{className:`h-4 w-4 absolute cursor-pointer top-0 z-10 pointer-events-auto 
        ${e.senderId===S.uid?"right-2 text-white ":"-left-5 text-black"}`,onClick:()=>D(e.id)})}),e.senderId!==S.uid&&o.jsx(iG,{onSelect:t=>H(e.id,t)})]},n)})]})}),o.jsx(i3.eW,{className:"bg-[#ffffff] dark:bg-[#181818] rounded-b-lg p-2",children:(0,o.jsxs)("form",{onSubmit:t=>{t.preventDefault(),0!==a.trim().length&&(A(e,{senderId:S.uid,content:a,timestamp:new Date().toISOString(),replyTo:E||null},c),D(""))},className:"flex flex-col w-full mb-2",children:[E&&(0,o.jsxs)("div",{className:"flex items-center justify-between p-2 rounded-lg shadow-sm opacity-90 bg-white dark:bg-[#2D2D2D] mb-2 border-l-4 border-gray-400 dark:border-gray-500 ",children:[o.jsx("div",{className:"text-sm italic text-gray-600 dark:text-gray-300 overflow-hidden whitespace-nowrap text-ellipsis max-w-full",children:o.jsx("span",{className:"font-semibold",children:r.find(e=>e.id===E)?.content.replace(/\*/g,"")||"Message not found"})}),o.jsx(iZ.z,{onClick:()=>D(""),className:"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 bg-transparent hover:bg-gray-200 dark:hover:bg-gray-600 h-6 rounded-full",title:"Cancel Reply",variant:"ghost",children:o.jsx(g.Z,{className:"h-4 w-4"})})]}),(0,o.jsxs)("div",{className:"relative bg-[#ececec] dark:bg-[#333333] rounded-full border border-gray-300 dark:border-gray-600 p-1 flex items-center space-x-2",children:[o.jsx("div",{className:"sm:hidden",children:o.jsx("button",{onClick:()=>z(!P),className:"p-2 text-gray-500 dark:text-gray-400",children:o.jsx(v,{className:"h-5 w-5 text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-200"})})}),o.jsx("div",{className:`absolute bottom-full left-1/2 transform -translate-x-1/2 bg-white dark:bg-gray-800 p-3 rounded-lg shadow-lg transition-transform duration-300 ${P?"translate-y-0 opacity-100":"translate-y-5 opacity-0 pointer-events-none"}`,children:(0,o.jsxs)("div",{className:"flex justify-around space-x-3",children:[o.jsx("button",{onClick:O,className:"p-2",children:o.jsx(y,{className:"h-5 w-5"})}),o.jsx("button",{onClick:F,className:"p-2",children:o.jsx(b,{className:"h-5 w-5"})}),o.jsx("button",{onClick:B,className:"p-2",children:o.jsx(x,{className:"h-5 w-5"})}),o.jsx("button",{onClick:q,className:"p-2",children:o.jsx(k,{className:"h-5 w-5"})}),o.jsx("button",{onClick:R,className:"p-2",children:o.jsx(w.Z,{className:"h-5 w-5"})})]})}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[o.jsx(iZ.z,{size:"icon",variant:"ghost",title:"Text Formatting",className:"group text-gray-500 hidden md:flex dark:text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 bg-transparent hover:bg-gray-100 dark:hover:bg-gray-600 rounded-full",onClick:()=>{I(e=>!e)},children:o.jsx(v,{className:"h-4 w-4"})}),M&&(0,o.jsxs)("div",{className:"formatting-options",children:[o.jsx(iZ.z,{size:"icon",type:"button",onClick:O,title:"Bold",className:"group text-gray-500 dark:text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 bg-transparent hover:bg-gray-100 dark:hover:bg-gray-600 rounded-full",children:o.jsx(y,{className:"h-5 w-5 text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-200"})}),o.jsx(iZ.z,{type:"button",size:"icon",onClick:F,title:"Italics",className:"group text-gray-500 dark:text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 bg-transparent hover:bg-gray-100 dark:hover:bg-gray-600 rounded-full",children:o.jsx(b,{className:"h-5 w-5 text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-200"})}),o.jsx(iZ.z,{type:"button",size:"icon",onClick:B,title:"Underline",className:"group text-gray-500 dark:text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 bg-transparent hover:bg-gray-100 dark:hover:bg-gray-600 rounded-full",children:o.jsx(x,{className:"h-5 w-5 text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-200"})})]})]}),o.jsx("textarea",{ref:N,className:"w-full flex-1 h-10 max-h-32 resize-none border-none p-2 bg-transparent placeholder-gray-500 dark:placeholder-gray-400 text-gray-800 dark:text-gray-100 focus:outline-none",placeholder:"Type message",value:a,rows:1,onChange:e=>c(e.target.value),onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||e.ctrlKey||(e.preventDefault(),a.trim().length>0&&(C(!0),setTimeout(()=>{c(""),C(!1)},1e3)))}}),o.jsx("button",{disabled:!a.trim().length||h,className:"p-2 flex md:hidden disabled:text-gray-600",children:h?o.jsx(s.Z,{className:"h-5 w-5 animate-spin "}):o.jsx(_.Z,{className:"h-5 w-5"})}),(0,o.jsxs)("div",{className:"hidden sm:flex items-center space-x-2 pr-2",children:[o.jsx("button",{onClick:q,className:"p-2",children:o.jsx(k,{className:"h-5 w-5"})}),o.jsx("button",{onClick:R,className:"p-2",children:o.jsx(w.Z,{className:"h-5 w-5"})}),o.jsx("button",{disabled:!a.trim().length||h,className:"p-2 disabled:text-gray-600",children:h?o.jsx(s.Z,{className:"h-5 w-5 animate-spin"}):o.jsx(_.Z,{className:"h-5 w-5"})})]})]})]})})]})})}n(84097);var i7=n(46319),i9=n(48586);let i8=()=>{let e=(0,u.v9)(e=>e.user),[t,n]=(0,l.useState)([]),[r,i]=(0,l.useState)(t[0]),[a,h]=(0,l.useState)(!0);return(0,l.useEffect)(()=>{let t;return(async()=>{h(!0),t=await (0,i4.K5)("conversations",e.uid,e=>{n(e),h(!1)})})(),()=>{t&&t()}},[e.uid]),(0,l.useEffect)(()=>{!r&&t.length>0&&i(t[0])},[t,r]),(0,o.jsxs)("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[o.jsx(p.Z,{menuItemsTop:"business"===e.type?i7.yn:i9.yL,menuItemsBottom:"business"===e.type?i7.$C:i9.$C,active:"Chats",conversations:t,setActiveConversation:i,activeConversation:r}),(0,o.jsxs)("div",{className:"flex flex-col mb-8 sm:gap-8 sm:py-0 sm:pl-14",children:[o.jsx(d.Z,{menuItemsTop:"business"===e.type?i7.yn:i9.yL,menuItemsBottom:"business"===e.type?i7.$C:i9.$C,activeMenu:"Chats",conversations:t,setActiveConversation:i,activeConversation:r,breadcrumbItems:[{label:"Freelancer",link:"/dashboard/freelancer"},{label:"Chats",link:"/dashboard/chats"}],searchPlaceholder:"Search..."}),o.jsx("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-4 lg:grid-cols-3 xl:grid-cols-3",children:a?o.jsx("div",{className:"col-span-3 flex justify-center items-center p-5",children:o.jsx(s.Z,{className:"h-6 w-6 text-primary animate-spin"})}):t.length>0?o.jsx(o.Fragment,{children:o.jsx(i6,{conversation:r,conversations:t,setActiveConversation:i})}):(0,o.jsxs)("div",{className:"col-span-3 flex flex-col items-center justify-center h-full px-4 py-16 text-center text-muted-foreground",children:[o.jsx(c.Z,{className:"w-10 h-10 mb-2"}),o.jsx("p",{className:"text-lg font-medium",children:"No conversations found"}),o.jsx("p",{className:"text-sm",children:"Start a new chat or wait for others to connect!"})]})})]})]})}},46319:(e,t,n)=>{"use strict";n.d(t,{$C:()=>v,Ne:()=>y,yn:()=>g});var r=n(10326),i=n(95920),o=n(57671),l=n(94909),a=n(12070),s=n(66307),c=n(69669),u=n(40617),d=n(69515),p=n(88378),h=n(40900),f=n(98091),m=n(46226);let g=[{href:"#",icon:r.jsx(m.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:r.jsx(i.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/business/market",icon:r.jsx(o.Z,{className:"h-5 w-5"}),label:"Market"},{href:"/business/talent",icon:r.jsx(l.Z,{className:"h-5 w-5"}),label:"Dehix Talent",subItems:[{label:"Overview",href:"/business/talent",icon:r.jsx(l.Z,{className:"h-4 w-4"})},{label:"Invites",href:"/business/market/invited",icon:r.jsx(a.Z,{className:"h-4 w-4"})},{label:"Accepted",href:"/business/market/accepted",icon:r.jsx(s.Z,{className:"h-4 w-4"})},{label:"Rejected",href:"/business/market/rejected",icon:r.jsx(c.Z,{className:"h-4 w-4"})}]},{href:"/chat",icon:r.jsx(u.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:r.jsx(d.Z,{className:"h-5 w-5"}),label:"Notes"}],v=[{href:"/business/settings/business-info",icon:r.jsx(p.Z,{className:"h-5 w-5"}),label:"Settings"}],y=[{href:"#",icon:r.jsx(m.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:r.jsx(i.Z,{className:"h-5 w-5"}),label:"Home"},{href:"/notes",icon:r.jsx(d.Z,{className:"h-5 w-5"}),label:"Notes"},{href:"/notes/archive",icon:r.jsx(h.Z,{className:"h-5 w-5"}),label:"Archive"},{href:"/notes/trash",icon:r.jsx(f.Z,{className:"h-5 w-5"}),label:"Trash"}]},48586:(e,t,n)=>{"use strict";n.d(t,{yL:()=>k,$C:()=>x,yn:()=>b});var r=n(10326),i=n(95920),o=n(80851);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,o.Z)("Store",[["path",{d:"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7",key:"ztvudi"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["path",{d:"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4",key:"2ebpfo"}],["path",{d:"M2 7h20",key:"1fcdvo"}],["path",{d:"M22 7v3a2 2 0 0 1-2 2v0a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12v0a2 2 0 0 1-2-2V7",key:"jon5kx"}]]),a=(0,o.Z)("BriefcaseBusiness",[["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2",key:"1ksdt3"}],["path",{d:"M22 13a18.15 18.15 0 0 1-20 0",key:"12hx5q"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]]);var s=n(43727);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let c=(0,o.Z)("TabletSmartphone",[["rect",{width:"10",height:"14",x:"3",y:"8",rx:"2",key:"1vrsiq"}],["path",{d:"M5 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2h-2.4",key:"1j4zmg"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]),u=(0,o.Z)("CalendarClock",[["path",{d:"M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5",key:"1osxxc"}],["path",{d:"M16 2v4",key:"4m81vk"}],["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M3 10h5",key:"r794hk"}],["path",{d:"M17.5 17.5 16 16.3V14",key:"akvzfd"}],["circle",{cx:"16",cy:"16",r:"6",key:"qoo3c4"}]]);var d=n(60763);/**
 * @license lucide-react v0.367.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let p=(0,o.Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]]);var h=n(40617),f=n(69515),m=n(88378),g=n(40900),v=n(98091),y=n(46226);let b=[{href:"#",icon:r.jsx(y.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/freelancer",icon:r.jsx(i.Z,{className:"h-5 w-5"}),label:"Dashboard"},{href:"/freelancer/market",icon:r.jsx(l,{className:"h-5 w-5"}),label:"Market"},{href:"/freelancer/project/current",icon:r.jsx(a,{className:"h-5 w-5"}),label:"Projects"},{href:"#",icon:r.jsx(s.Z,{className:"h-5 w-5 cursor-not-allowed"}),label:"Analytics"},{href:"/freelancer/interview/profile",icon:r.jsx(c,{className:"h-5 w-5"}),label:"Interviews"},{href:"#",icon:r.jsx(u,{className:"h-5 w-5 cursor-not-allowed"}),label:"Schedule Interviews"},{href:"/freelancer/oracleDashboard/businessVerification",icon:r.jsx(d.Z,{className:"h-5 w-5"}),label:"Oracle"},{href:"/freelancer/talent",icon:r.jsx(p,{className:"h-5 w-5"}),label:"Talent"},{href:"/chat",icon:r.jsx(h.Z,{className:"h-5 w-5"}),label:"Chats"},{href:"/notes",icon:r.jsx(f.Z,{className:"h-5 w-5"}),label:"Notes"}],x=[{href:"/freelancer/settings/personal-info",icon:r.jsx(m.Z,{className:"h-5 w-5"}),label:"Settings"}];y.default,i.Z,f.Z,g.Z,v.Z;let k=[{href:"#",icon:r.jsx(y.default,{src:"/dehix.png",alt:"Icon",width:16,height:16,className:"transition-all group-hover:scale-110 invert dark:invert-0"}),label:"Dehix"},{href:"/dashboard/business",icon:r.jsx(i.Z,{className:"h-5 w-5"}),label:"Home"}]},34356:(e,t,n)=>{"use strict";n.r(t),n.d(t,{$$typeof:()=>l,__esModule:()=>o,default:()=>a});var r=n(68570);let i=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\chat\page.tsx`),{__esModule:o,$$typeof:l}=i;i.default;let a=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\Dehix\dehix_alpha_frontend\src\app\chat\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[8948,4198,6034,4718,6226,495,5645,2146,1375,7926,2637,4736,6499,8066,588],()=>n(62730));module.exports=r})();